using Serilog;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Forms;
using SM_Gestion_Documentos.Services;
using System.Globalization;
using Microsoft.EntityFrameworkCore;

namespace SM_Gestion_Documentos
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            // Configurar cultura
            Thread.CurrentThread.CurrentCulture = new CultureInfo("es-AR");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("es-AR");

            // Configurar Serilog
            ConfigureLogging();

            try
            {
                Log.Information("Iniciando aplicaci�n Document Manager");

                // Configurar aplicaci�n
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetHighDpiMode(HighDpiMode.SystemAware);

                // Inicializar base de datos
                InitializeDatabase();

                // Crear servicios
                var services = CreateServices();

                // Mostrar formulario de login
                using var loginForm = new LoginForm(services.AuthService, services.AuditService);

                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    Log.Information("Login exitoso, verificando contraseña por defecto");

                    // Verificar si el admin necesita cambiar su contraseña
                    if (services.AuthService.IsUsingDefaultPassword)
                    {
                        Log.Warning("Admin user is using default password, forcing password change");

                        MessageBox.Show(
                            "Por seguridad, debe cambiar la contraseña por defecto antes de continuar.\n\n" +
                            "La nueva contraseña debe tener:\n" +
                            "• Mínimo 8 caracteres\n" +
                            "• Al menos 1 mayúscula\n" +
                            "• Al menos 1 número\n" +
                            "• Al menos 1 símbolo",
                            "Cambio de Contraseña Obligatorio",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);

                        using var changePasswordForm = new ChangePasswordForm(services.UserService, services.AuthService, true);

                        if (changePasswordForm.ShowDialog() != DialogResult.OK)
                        {
                            Log.Information("Password change cancelled, exiting application");
                            return;
                        }

                        Log.Information("Password changed successfully");
                    }

                    Log.Information("Mostrando formulario principal");

                    // Mostrar formulario principal
                    using var mainForm = new MainForm(
                        services.Context,
                        services.AuthService,
                        services.DocumentService,
                        services.OcrService,
                        services.StorageService,
                        services.AuditService,
                        services.ScannerService);

                    Application.Run(mainForm);
                }
                else
                {
                    Log.Information("Login cancelado por el usuario");
                }
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Error fatal en la aplicaci�n");
                MessageBox.Show(
                    $"Error fatal en la aplicaci�n:\n\n{ex.Message}\n\nLa aplicaci�n se cerrar�.",
                    "Error Fatal",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                Log.Information("Cerrando aplicaci�n Document Manager");
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureLogging()
        {
            var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            Directory.CreateDirectory(logPath);

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File(
                    path: Path.Combine(logPath, "app-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();
        }

        private static void InitializeDatabase()
        {
            try
            {
                Log.Information("Inicializando base de datos");

                using var context = new DatabaseContext();

                // Crear base de datos si no existe
                context.Database.EnsureCreated();

                // Aplicar migraciones pendientes
                if (context.Database.GetPendingMigrations().Any())
                {
                    Log.Information("Aplicando migraciones de base de datos");
                    context.Database.Migrate();
                }

                // Verificar que existe al menos un usuario administrador
                if (!context.Users.Any(u => u.Role == "Admin"))
                {
                    Log.Information("Creando usuario administrador por defecto");
                    var adminUser = new Models.User
                    {
                        Username = "admin",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        FullName = "Administrador del Sistema",
                        Email = "<EMAIL>",
                        Role = "Admin",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    context.Users.Add(adminUser);
                    context.SaveChanges();

                    Log.Information("Usuario administrador creado: admin/admin123");
                }

                Log.Information("Base de datos inicializada correctamente");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al inicializar la base de datos");
                throw new Exception($"Error al inicializar la base de datos: {ex.Message}", ex);
            }
        }

        private static ServiceContainer CreateServices()
        {
            try
            {
                Log.Information("Creando servicios de la aplicaci�n");

                var context = new DatabaseContext();
                var auditService = new AuditService(context);
                var authService = new AuthenticationService(context, auditService);
                var userService = new UserService(context, auditService, authService);
                var storageService = new StorageService();
                var ocrService = new OCRService();
                var scannerService = new ScannerService();
                var documentService = new DocumentService(context, auditService, storageService, authService);

                Log.Information("Servicios creados correctamente");

                return new ServiceContainer
                {
                    Context = context,
                    AuditService = auditService,
                    AuthService = authService,
                    UserService = userService,
                    StorageService = storageService,
                    OcrService = ocrService,
                    ScannerService = scannerService,
                    DocumentService = documentService
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al crear servicios");
                throw new Exception($"Error al crear servicios: {ex.Message}", ex);
            }
        }

        private class ServiceContainer
        {
            public DatabaseContext Context { get; set; } = null!;
            public AuditService AuditService { get; set; } = null!;
            public AuthenticationService AuthService { get; set; } = null!;
            public UserService UserService { get; set; } = null!;
            public StorageService StorageService { get; set; } = null!;
            public OCRService OcrService { get; set; } = null!;
            public ScannerService ScannerService { get; set; } = null!;
            public DocumentService DocumentService { get; set; } = null!;
        }
    }
}