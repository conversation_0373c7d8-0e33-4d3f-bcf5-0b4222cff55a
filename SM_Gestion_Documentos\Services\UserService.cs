using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Models;
using Serilog;

namespace SM_Gestion_Documentos.Services
{
    public class UserService
    {
        private readonly DatabaseContext _context;
        private readonly AuditService _auditService;
        private readonly AuthenticationService _authService;

        public UserService(DatabaseContext context, AuditService auditService, AuthenticationService authService)
        {
            _context = context;
            _auditService = auditService;
            _authService = authService;
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users.OrderBy(u => u.Username).ToListAsync();
        }

        public async Task<List<User>> SearchUsersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllUsersAsync();

            return await _context.Users
                .Where(u => u.Username.Contains(searchTerm) || u.FullName.Contains(searchTerm) || u.Email.Contains(searchTerm))
                .OrderBy(u => u.Username)
                .ToListAsync();
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == user.Username);
            if (existingUser != null)
                throw new InvalidOperationException("Ya existe un usuario con ese nombre.");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            await _auditService.LogAsync("Create", "User", user.Id, _authService.CurrentUser?.Username,
                Environment.MachineName, $"Usuario creado: {user.Username}");

            Log.Information("Usuario {Username} creado por {CurrentUser}", user.Username, _authService.CurrentUser?.Username);
            return user;
        }

        public async Task<User> UpdateUserAsync(User user, string? newPassword = null)
        {
            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null)
                throw new InvalidOperationException("Usuario no encontrado.");

            existingUser.FullName = user.FullName;
            existingUser.Email = user.Email;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;

            if (!string.IsNullOrWhiteSpace(newPassword))
            {
                existingUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            }

            await _context.SaveChangesAsync();

            await _auditService.LogAsync("Update", "User", user.Id, _authService.CurrentUser?.Username,
                Environment.MachineName, $"Usuario actualizado: {user.Username}");

            Log.Information("Usuario {Username} actualizado por {CurrentUser}", user.Username, _authService.CurrentUser?.Username);
            return existingUser;
        }

        public async Task DeleteUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                throw new InvalidOperationException("Usuario no encontrado.");

            _context.Users.Remove(user);
            await _context.SaveChangesAsync();

            await _auditService.LogAsync("Delete", "User", userId, _authService.CurrentUser?.Username,
                Environment.MachineName, $"Usuario eliminado: {user.Username}");

            Log.Information("Usuario {Username} eliminado por {CurrentUser}", user.Username, _authService.CurrentUser?.Username);
        }
    }
}