﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Models;
using Serilog;
using Microsoft.EntityFrameworkCore;

namespace SM_Gestion_Documentos.Services
{
    public class AuditService
    {
        private readonly DatabaseContext _context;

        public AuditService(DatabaseContext context)
        {
            _context = context;
        }

        public async Task LogAsync(string action, string? entityType = null, int? entityId = null,
            string? username = null, string? ipAddress = null, string? details = null,
            bool success = true, string? errorMessage = null)
        {
            try
            {
                var auditLog = new AuditLog
                {
                    Action = action,
                    EntityType = entityType ?? string.Empty,
                    EntityId = entityId,
                    Username = username ?? "System",
                    IPAddress = ipAddress ?? Environment.MachineName,
                    Details = details ?? string.Empty,
                    Success = success,
                    ErrorMessage = errorMessage ?? string.Empty,
                    Timestamp = DateTime.Now
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();

                // También registrar en Serilog
                if (success)
                {
                    Log.Information("Audit: {Action} by {Username} - {Details}",
                        action, username, details);
                }
                else
                {
                    Log.Warning("Audit: Failed {Action} by {Username} - {ErrorMessage}",
                        action, username, errorMessage);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error writing audit log for action: {Action}", action);
            }
        }

        public async Task<List<AuditLog>> GetAuditLogsAsync(DateTime? fromDate = null,
            DateTime? toDate = null, string? username = null, string? action = null, int limit = 100)
        {
            try
            {
                var query = _context.AuditLogs.AsQueryable();

                if (fromDate.HasValue)
                    query = query.Where(a => a.Timestamp >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(a => a.Timestamp <= toDate.Value);

                if (!string.IsNullOrWhiteSpace(username))
                    query = query.Where(a => a.Username.Contains(username));

                if (!string.IsNullOrWhiteSpace(action))
                    query = query.Where(a => a.Action.Contains(action));

                return await query
                    .OrderByDescending(a => a.Timestamp)
                    .Take(limit)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving audit logs");
                return new List<AuditLog>();
            }
        }

        public async Task CleanupOldLogsAsync(int daysToKeep = 365)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var oldLogs = _context.AuditLogs.Where(a => a.Timestamp < cutoffDate);

                _context.AuditLogs.RemoveRange(oldLogs);
                var deletedCount = await _context.SaveChangesAsync();

                Log.Information("Cleaned up {Count} old audit logs", deletedCount);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error cleaning up old audit logs");
            }
        }
    }
}
