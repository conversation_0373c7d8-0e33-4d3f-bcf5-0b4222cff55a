using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Models;
using Microsoft.EntityFrameworkCore;
using System.DirectoryServices;
using Serilog;

namespace SM_Gestion_Documentos.Services
{
    public class AuthenticationService
    {
        private readonly DatabaseContext _context;
        private readonly AuditService _auditService;
        private User? _currentUser;

        public AuthenticationService(DatabaseContext context, AuditService auditService)
        {
            _context = context;
            _auditService = auditService;
        }

        public User? CurrentUser => _currentUser;

        public bool IsUsingDefaultPassword => _currentUser?.Username == "admin" &&
                                            BCrypt.Net.BCrypt.Verify("admin123", _currentUser.PasswordHash);

        public async Task<bool> LoginAsync(string username, string password, bool useActiveDirectory = false)
        {
            try
            {
                if (useActiveDirectory)
                {
                    return await LoginWithActiveDirectoryAsync(username, password);
                }
                else
                {
                    return await LoginWithDatabaseAsync(username, password);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error durante el inicio de sesión para el usuario {Username}", username);
                await _auditService.LogAsync("Login", null, null, username,
                    Environment.MachineName, $"Inicio de sesión fallido: {ex.Message}", false, ex.Message);
                return false;
            }
        }

        private async Task<bool> LoginWithDatabaseAsync(string username, string password)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

            if (user != null && BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            {
                _currentUser = user;
                user.LastLogin = DateTime.Now;
                await _context.SaveChangesAsync();

                await _auditService.LogAsync("Login", "User", user.Id, username,
                    Environment.MachineName, "Inicio de sesión exitoso en base de datos");

                Log.Information("Usuario {Username} inició sesión exitosamente", username);
                return true;
            }

            await _auditService.LogAsync("Login", null, null, username,
                Environment.MachineName, "Credenciales inválidas", false, "Usuario o contraseña inválidos");

            return false;
        }

        private async Task<bool> LoginWithActiveDirectoryAsync(string username, string password)
        {
            try
            {
                using var entry = new DirectoryEntry("LDAP://your-domain.com", username, password);
                using var searcher = new DirectorySearcher(entry);
                searcher.Filter = $"(sAMAccountName={username})";
                var result = searcher.FindOne();

                if (result != null)
                {
                    // Crear o actualizar usuario en la base de datos local
                    var user = await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
                    if (user == null)
                    {
                        user = new User
                        {
                            Username = username,
                            FullName = result.Properties["displayName"]?[0]?.ToString() ?? username,
                            Email = result.Properties["mail"]?[0]?.ToString() ?? "",
                            Role = "User",
                            IsActive = true
                        };
                        _context.Users.Add(user);
                    }

                    user.LastLogin = DateTime.Now;
                    await _context.SaveChangesAsync();

                    _currentUser = user;

                    await _auditService.LogAsync("Login", "User", user.Id, username,
                        Environment.MachineName, "Inicio de sesión exitoso por AD");

                    Log.Information("Usuario {Username} inició sesión vía Active Directory", username);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error de inicio de sesión de Active Directory para el usuario {Username}", username);
            }

            await _auditService.LogAsync("Login", null, null, username,
                Environment.MachineName, "Autenticación AD fallida", false, "Autenticación de Active Directory fallida");

            return false;
        }

        public async Task LogoutAsync()
        {
            if (_currentUser != null)
            {
                await _auditService.LogAsync("Logout", "User", _currentUser.Id, _currentUser.Username,
                    Environment.MachineName, "Usuario cerró sesión");

                Log.Information("Usuario {Username} cerró sesión", _currentUser.Username);
                _currentUser = null;
            }
        }

        public bool HasPermission(string permission)
        {
            if (_currentUser == null) return false;
            
            return permission switch
            {
                "ManageUsers" => _currentUser.Role == "Admin",
                "ViewAuditLogs" => _currentUser.Role == "Admin" || _currentUser.Role == "Manager",
                "DeleteDocuments" => _currentUser.Role == "Admin" || _currentUser.Role == "Manager",
                _ => true
            };
        }
    }
}
