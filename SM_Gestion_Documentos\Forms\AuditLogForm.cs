using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Models;
using SM_Gestion_Documentos.Services;

namespace SM_Gestion_Documentos.Forms
{
    public partial class AuditLogForm : Form
    {
        private readonly AuditService _auditService;
        private readonly AuthenticationService _authService;

        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private TextBox txtUsername;
        private ComboBox cmbAction;
        private Button btnSearch;
        private Button btnRefresh;
        private Button btnExport;
        private Button btnClear;
        private DataGridView dgvAuditLogs;

        public AuditLogForm(AuditService auditService, AuthenticationService authService)
        {
            _auditService = auditService;
            _authService = authService;
            InitializeComponent();

            // Cargar datos después de que el formulario se muestre
            this.Load += async (s, e) => await LoadAuditLogsAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "Logs de Auditoría";
            this.Size = new Size(1100, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Panel superior - Filtros
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                Padding = new Padding(10)
            };

            var grpFilters = new GroupBox
            {
                Text = "Filtros",
                Dock = DockStyle.Fill
            };

            var lblFromDate = new Label
            {
                Text = "Desde:",
                Location = new Point(10, 25),
                Size = new Size(50, 20)
            };

            dtpFromDate = new DateTimePicker
            {
                Location = new Point(65, 23),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddDays(-1)
            };

            var lblToDate = new Label
            {
                Text = "Hasta:",
                Location = new Point(200, 25),
                Size = new Size(50, 20)
            };

            dtpToDate = new DateTimePicker
            {
                Location = new Point(255, 23),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            var lblUsername = new Label
            {
                Text = "Usuario:",
                Location = new Point(390, 25),
                Size = new Size(50, 20)
            };

            txtUsername = new TextBox
            {
                Location = new Point(445, 23),
                Size = new Size(100, 23)
            };

            var lblAction = new Label
            {
                Text = "Acción:",
                Location = new Point(560, 25),
                Size = new Size(50, 20)
            };

            cmbAction = new ComboBox
            {
                Location = new Point(615, 23),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbAction.Items.AddRange(new[] { "", "Login", "Logout", "Create", "Update", "Delete", "View", "Search" });

            btnSearch = new Button
            {
                Text = "Buscar",
                Location = new Point(730, 22),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSearch.Click += BtnSearch_Click;

            btnRefresh = new Button
            {
                Text = "Actualizar",
                Location = new Point(810, 22),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnRefresh.Click += BtnRefresh_Click;

            btnClear = new Button
            {
                Text = "Limpiar",
                Location = new Point(890, 22),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnClear.Click += BtnClear_Click;

            btnExport = new Button
            {
                Text = "Exportar",
                Location = new Point(970, 22),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnExport.Click += BtnExport_Click;

            grpFilters.Controls.AddRange(new Control[] {
                lblFromDate, dtpFromDate, lblToDate, dtpToDate,
                lblUsername, txtUsername, lblAction, cmbAction,
                btnSearch, btnRefresh, btnClear, btnExport
            });

            topPanel.Controls.Add(grpFilters);

            // Panel principal - Grid
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            dgvAuditLogs = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            // Configurar columnas
            dgvAuditLogs.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "Fecha/Hora",
                    DataPropertyName = "Timestamp",
                    Width = 130
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Username",
                    HeaderText = "Usuario",
                    DataPropertyName = "Username",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Action",
                    HeaderText = "Acción",
                    DataPropertyName = "Action",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "EntityType",
                    HeaderText = "Tipo",
                    DataPropertyName = "EntityType",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "EntityId",
                    HeaderText = "ID",
                    DataPropertyName = "EntityId",
                    Width = 60
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "IPAddress",
                    HeaderText = "IP/Máquina",
                    DataPropertyName = "IPAddress",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Details",
                    HeaderText = "Detalles",
                    DataPropertyName = "Details",
                    Width = 250
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "Success",
                    HeaderText = "Éxito",
                    DataPropertyName = "Success",
                    Width = 60
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "ErrorMessage",
                    HeaderText = "Error",
                    DataPropertyName = "ErrorMessage",
                    Width = 150
                }
            });

            // Formatear filas según el resultado
            dgvAuditLogs.CellFormatting += DgvAuditLogs_CellFormatting;

            mainPanel.Controls.Add(dgvAuditLogs);

            this.Controls.AddRange(new Control[] { topPanel, mainPanel });
        }

        private void DgvAuditLogs_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvAuditLogs.Rows[e.RowIndex].DataBoundItem is AuditLog auditLog)
            {
                if (!auditLog.Success)
                {
                    dgvAuditLogs.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 238);
                    dgvAuditLogs.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                }
                else if (auditLog.Action == "Login")
                {
                    dgvAuditLogs.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(212, 237, 218);
                }
                else if (auditLog.Action == "Delete")
                {
                    dgvAuditLogs.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(248, 215, 218);
                }
            }
        }

        private async Task LoadAuditLogsAsync()
        {
            try
            {
                // Cargar los últimos registros (últimas 24 horas por defecto)
                var fromDate = DateTime.Now.AddDays(-1);
                var toDate = DateTime.Now;

                var logs = await _auditService.GetAuditLogsAsync(
                    fromDate,
                    toDate,
                    null, null, 500);

                dgvAuditLogs.DataSource = logs;
                this.Text = $"Logs de Auditoría - {logs.Count} registros (últimas 24 horas)";

                // Actualizar los controles de fecha para reflejar el rango cargado
                dtpFromDate.Value = fromDate;
                dtpToDate.Value = toDate;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al cargar logs de auditoría: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                btnSearch.Enabled = false;
                btnSearch.Text = "Buscando...";

                var username = string.IsNullOrWhiteSpace(txtUsername.Text) ? null : txtUsername.Text.Trim();
                var action = cmbAction.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(action)) action = null;

                // Asegurar que obtenemos hasta el final del día seleccionado
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var logs = await _auditService.GetAuditLogsAsync(
                    fromDate, toDate, username, action, 1000);

                dgvAuditLogs.DataSource = logs;

                var filterInfo = "";
                if (!string.IsNullOrEmpty(username)) filterInfo += $" Usuario: {username}";
                if (!string.IsNullOrEmpty(action)) filterInfo += $" Acción: {action}";

                this.Text = $"Logs de Auditoría - {logs.Count} registros encontrados{filterInfo}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al buscar logs: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSearch.Enabled = true;
                btnSearch.Text = "Buscar";
            }
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                btnRefresh.Enabled = false;
                btnRefresh.Text = "Actualizando...";

                // Actualizar la fecha "hasta" al momento actual
                dtpToDate.Value = DateTime.Now;

                // Realizar la búsqueda con los filtros actuales
                var username = string.IsNullOrWhiteSpace(txtUsername.Text) ? null : txtUsername.Text.Trim();
                var action = cmbAction.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(action)) action = null;

                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var logs = await _auditService.GetAuditLogsAsync(
                    fromDate, toDate, username, action, 1000);

                dgvAuditLogs.DataSource = logs;

                var filterInfo = "";
                if (!string.IsNullOrEmpty(username)) filterInfo += $" Usuario: {username}";
                if (!string.IsNullOrEmpty(action)) filterInfo += $" Acción: {action}";

                this.Text = $"Logs de Auditoría - {logs.Count} registros actualizados{filterInfo}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al actualizar logs: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnRefresh.Enabled = true;
                btnRefresh.Text = "Actualizar";
            }
        }

        private async void BtnClear_Click(object sender, EventArgs e)
        {
            txtUsername.Clear();
            cmbAction.SelectedIndex = -1;
            dtpFromDate.Value = DateTime.Now.AddDays(-1);
            dtpToDate.Value = DateTime.Now;
            await LoadAuditLogsAsync();
        }

        private async void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "Exportar Logs de Auditoría",
                    Filter = "Archivo CSV|*.csv|Archivo de Texto|*.txt",
                    FileName = $"AuditLogs_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    await ExportAuditLogs(saveFileDialog.FileName);
                    MessageBox.Show("Logs exportados exitosamente.",
                        "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al exportar logs: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task ExportAuditLogs(string filePath)
        {
            var logs = dgvAuditLogs.DataSource as List<AuditLog>;
            if (logs == null || !logs.Any())
                return;

            using var writer = new StreamWriter(filePath, false, System.Text.Encoding.UTF8);

            // Escribir encabezados
            await writer.WriteLineAsync("Fecha/Hora,Usuario,Acción,Tipo,ID,IP/Máquina,Detalles,Éxito,Error");

            // Escribir datos
            foreach (var log in logs)
            {
                var line = $"\"{log.Timestamp:yyyy-MM-dd HH:mm:ss}\"," +
                          $"\"{log.Username}\"," +
                          $"\"{log.Action}\"," +
                          $"\"{log.EntityType}\"," +
                          $"\"{log.EntityId}\"," +
                          $"\"{log.IPAddress}\"," +
                          $"\"{log.Details.Replace("\"", "\"\"")}\"," +
                          $"\"{(log.Success ? "Sí" : "No")}\"," +
                          $"\"{log.ErrorMessage.Replace("\"", "\"\"")}\"";

                await writer.WriteLineAsync(line);
            }
        }
    }
}
