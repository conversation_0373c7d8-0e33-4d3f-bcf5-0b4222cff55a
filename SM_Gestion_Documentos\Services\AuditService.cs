﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Models;
using Serilog;
using Microsoft.EntityFrameworkCore;

namespace SM_Gestion_Documentos.Services
{
    public class AuditService
    {
        private readonly DatabaseContext _context;

        public AuditService(DatabaseContext context)
        {
            _context = context;
        }

        public async Task LogAsync(string action, string? entityType = null, int? entityId = null,
            string? username = null, string? ipAddress = null, string? details = null,
            bool success = true, string? errorMessage = null)
        {
            try
            {
                var auditLog = new AuditLog
                {
                    Action = action,
                    EntityType = entityType ?? string.Empty,
                    EntityId = entityId,
                    Username = username ?? "System",
                    IPAddress = ipAddress ?? Environment.MachineName,
                    Details = details ?? string.Empty,
                    Success = success,
                    ErrorMessage = errorMessage ?? string.Empty,
                    Timestamp = DateTime.Now
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();

                // También registrar en Serilog
                if (success)
                {
                    Log.Information("Auditoría: {Action} por {Username} - {Details}",
                        action, username, details);
                }
                else
                {
                    Log.Warning("Auditoría: {Action} fallida por {Username} - {ErrorMessage}",
                        action, username, errorMessage);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al escribir log de auditoría para la acción: {Action}", action);
            }
        }

        public async Task<List<AuditLog>> GetAuditLogsAsync(DateTime? fromDate = null,
            DateTime? toDate = null, string? username = null, string? action = null, string? entityType = null, int limit = 100)
        {
            try
            {
                // Limpiar el cache del contexto para obtener datos frescos
                var trackedEntries = _context.ChangeTracker.Entries<AuditLog>()
                    .Where(e => e.State != EntityState.Deleted)
                    .ToList();

                foreach (var entry in trackedEntries)
                {
                    await entry.ReloadAsync();
                }

                var query = _context.AuditLogs.AsNoTracking().AsQueryable();

                if (fromDate.HasValue)
                    query = query.Where(a => a.Timestamp >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(a => a.Timestamp <= toDate.Value);

                if (!string.IsNullOrWhiteSpace(username))
                    query = query.Where(a => a.Username.Contains(username));

                if (!string.IsNullOrWhiteSpace(action))
                    query = query.Where(a => a.Action.Contains(action));

                if (!string.IsNullOrWhiteSpace(entityType))
                    query = query.Where(a => a.EntityType.Contains(entityType));

                var result = await query
                    .OrderByDescending(a => a.Timestamp)
                    .Take(limit)
                    .ToListAsync();

                Log.Information("Se obtuvieron {Count} logs de auditoría desde {FromDate} hasta {ToDate}",
                    result.Count, fromDate, toDate);

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al obtener logs de auditoría");
                return new List<AuditLog>();
            }
        }

        public async Task CleanupOldLogsAsync(int daysToKeep = 365)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var oldLogs = _context.AuditLogs.Where(a => a.Timestamp < cutoffDate);

                _context.AuditLogs.RemoveRange(oldLogs);
                var deletedCount = await _context.SaveChangesAsync();

                Log.Information("Se limpiaron {Count} logs de auditoría antiguos", deletedCount);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al limpiar logs de auditoría antiguos");
            }
        }
    }
}
