﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tesseract;
using System.Drawing;
using System.Text.RegularExpressions;
using Serilog;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;

namespace SM_Gestion_Documentos.Services
{
    public class OCRService : IDisposable
    {
        private TesseractEngine? _engine;
        private readonly string _tessDataPath;

        public OCRService()
        {
            _tessDataPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tessdata");
            InitializeEngine();
        }

        private void InitializeEngine()
        {
            try
            {
                if (!Directory.Exists(_tessDataPath))
                {
                    Directory.CreateDirectory(_tessDataPath);
                    Log.Warning("Directorio de datos de Tesseract no encontrado. Por favor descargue los archivos tessdata.");
                }

                _engine = new TesseractEngine(_tessDataPath, "spa", EngineMode.Default);
                _engine.SetVariable("tessedit_char_whitelist", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al inicializar el motor OCR");
                throw;
            }
        }

        public async Task<string?> ExtractDNIFromImageAsync(string imagePath)
        {
            try
            {
                if (_engine == null)
                {
                    Log.Error("Motor OCR no inicializado");
                    return null;
                }

                using var img = Pix.LoadFromFile(imagePath);
                using var page = _engine.Process(img);

                var text = page.GetText();
                Log.Debug("Texto extraído por OCR: {Text}", text);

                return ExtractDNIFromText(text);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI de la imagen {ImagePath}", imagePath);
                return null;
            }
        }

        public async Task<string?> ExtractDNIFromImageAsync(Bitmap bitmap)
        {
            try
            {
                if (_engine == null)
                {
                    Log.Error("Motor OCR no inicializado");
                    return null;
                }

                using var memoryStream = new MemoryStream();
                bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                memoryStream.Position = 0;

                using var img = Pix.LoadFromMemory(memoryStream.ToArray());
                using var page = _engine.Process(img);

                var text = page.GetText();
                Log.Debug("Texto extraído por OCR del bitmap: {Text}", text);

                return ExtractDNIFromText(text);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del bitmap");
                return null;
            }
        }

        private string? ExtractDNIFromText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return null;

            // Patrones para buscar DNI (8 dígitos)
            var patterns = new[]
            {
                @"\b\d{8}\b",                           // 8 dígitos exactos
                @"DNI[:\s]*(\d{8})",                   // DNI: 12345678
                @"D\.N\.I[:\s]*(\d{8})",              // D.N.I: 12345678
                @"DOCUMENTO[:\s]*(\d{8})",             // DOCUMENTO: 12345678
                @"N[°º]\s*(\d{8})",                    // N° 12345678
                @"(\d{2})\s*\.?\s*(\d{3})\s*\.?\s*(\d{3})" // 12.345.678 o 12 345 678
            };

            foreach (var pattern in patterns)
            {
                var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
                foreach (Match match in matches)
                {
                    string dni;
                    if (match.Groups.Count > 1)
                    {
                        // Si hay grupos de captura, usar el primer grupo
                        dni = match.Groups[1].Value;
                    }
                    else if (pattern.Contains(@"(\d{2})")) // Corregido: usar @ para raw string
                    {
                        // Para el patrón con puntos, concatenar los grupos
                        dni = match.Groups[1].Value + match.Groups[2].Value + match.Groups[3].Value;
                    }
                    else
                    {
                        dni = match.Value;
                    }

                    // Limpiar y validar
                    dni = Regex.Replace(dni, @"[^\d]", "");

                    if (dni.Length == 8 && IsValidDNI(dni))
                    {
                        Log.Information("DNI extraído exitosamente: {DNI}", dni);
                        return dni;
                    }
                }
            }

            Log.Warning("No se encontró un DNI válido en el texto");
            return null;
        }

        private bool IsValidDNI(string dni)
        {
            // Validaciones básicas para DNI argentino
            if (string.IsNullOrWhiteSpace(dni) || dni.Length != 8)
                return false;

            // No debe ser todo el mismo número
            if (dni.All(c => c == dni[0]))
                return false;

            // Debe ser numérico
            return dni.All(char.IsDigit);
        }

        public Bitmap PreprocessImage(Bitmap original)
        {
            try
            {
                // Crear una copia para procesar
                var processed = new Bitmap(original.Width, original.Height);

                using (var g = Graphics.FromImage(processed))
                {
                    // Mejorar contraste y convertir a escala de grises
                    var colorMatrix = new System.Drawing.Imaging.ColorMatrix(new float[][]
                    {
                        new float[] {0.299f, 0.299f, 0.299f, 0, 0},
                        new float[] {0.587f, 0.587f, 0.587f, 0, 0},
                        new float[] {0.114f, 0.114f, 0.114f, 0, 0},
                        new float[] {0, 0, 0, 1, 0},
                        new float[] {0, 0, 0, 0, 1}
                    });

                    var attributes = new System.Drawing.Imaging.ImageAttributes();
                    attributes.SetColorMatrix(colorMatrix);

                    g.DrawImage(original, new Rectangle(0, 0, original.Width, original.Height),
                        0, 0, original.Width, original.Height, GraphicsUnit.Pixel, attributes);
                }

                return processed;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al preprocesar la imagen");
                return original;
            }
        }

        public async Task<string?> ExtractDNIFromPDFAsync(string pdfPath)
        {
            try
            {
                Log.Information("Iniciando extracción de DNI desde PDF: {PdfPath}", pdfPath);

                // Primero intentar extraer texto directamente del PDF
                var pdfText = ExtractTextFromPDF(pdfPath);
                if (!string.IsNullOrEmpty(pdfText))
                {
                    var dni = ExtractDNIFromText(pdfText);
                    if (!string.IsNullOrEmpty(dni))
                    {
                        Log.Information("DNI extraído del texto del PDF: {DNI}", dni);
                        return dni;
                    }
                }

                Log.Information("No se encontró DNI en el texto del PDF, intentando OCR en imágenes");

                // Si no se encuentra DNI en el texto, intentar convertir páginas a imágenes y aplicar OCR
                // Nota: Esta funcionalidad requiere librerías adicionales como PdfiumViewer
                // Por ahora, retornamos null y sugerimos al usuario ingresar manualmente

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del PDF: {PdfPath}", pdfPath);
                return null;
            }
        }

        private string ExtractTextFromPDF(string pdfPath)
        {
            try
            {
                var text = new StringBuilder();

                using (var reader = new PdfReader(pdfPath))
                {
                    // Verificar que el PDF no esté protegido
                    if (reader.IsEncrypted())
                    {
                        Log.Warning("El PDF está protegido con contraseña: {PdfPath}", pdfPath);
                        return string.Empty;
                    }

                    // Extraer texto de las primeras 3 páginas (donde usualmente está el DNI)
                    int pagesToProcess = Math.Min(reader.NumberOfPages, 3);

                    for (int page = 1; page <= pagesToProcess; page++)
                    {
                        try
                        {
                            var pageText = PdfTextExtractor.GetTextFromPage(reader, page);
                            if (!string.IsNullOrWhiteSpace(pageText))
                            {
                                text.AppendLine(pageText);
                            }
                        }
                        catch (Exception pageEx)
                        {
                            Log.Warning(pageEx, "Error al extraer texto de la página {Page} del PDF", page);
                        }
                    }
                }

                Log.Debug("Texto extraído del PDF: {Text}", text.ToString());
                return text.ToString();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer texto del PDF");
                return string.Empty;
            }
        }

        public void Dispose()
        {
            _engine?.Dispose();
        }
    }
}
