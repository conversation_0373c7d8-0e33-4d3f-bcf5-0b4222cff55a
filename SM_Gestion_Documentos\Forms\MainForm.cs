using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Services;
using SM_Gestion_Documentos.Models;
using System.Diagnostics;
using Serilog;
using Timer = System.Windows.Forms.Timer;

namespace SM_Gestion_Documentos.Forms
{
    public partial class MainForm : Form
    {
        private readonly DatabaseContext _context;
        private readonly AuthenticationService _authService;
        private readonly DocumentService _documentService;
        private readonly OCRService _ocrService;
        private readonly StorageService _storageService;
        private readonly AuditService _auditService;
        private readonly ScannerService _scannerService;

        // Controles principales
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private SplitContainer splitContainer;
        private TabControl tabControl;

        // Controles de escaneo
        private Button btnScanDocument;
        private Button btnSelectFile;
        private TextBox txtDNI;
        private TextBox txtNotes;
        private PictureBox picPreview;

        // Controles de búsqueda
        private TextBox txtSearchDNI;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Button btnSearch;
        private DataGridView dgvDocuments;

        public MainForm(DatabaseContext context, AuthenticationService authService, DocumentService documentService,
            OCRService ocrService, StorageService storageService, AuditService auditService, ScannerService scannerService)
        {
            _context = context;
            _authService = authService;
            _documentService = documentService;
            _ocrService = ocrService;
            _storageService = storageService;
            _auditService = auditService;
            _scannerService = scannerService;

            InitializeComponent();
            LoadUserInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "Sistema de Gestión Documental - Municipalidad de San Miguel";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            CreateMenuStrip();
            CreateStatusStrip();
            CreateMainLayout();

            this.FormClosing += MainForm_FormClosing;
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();

            // Menú Archivo
            var fileMenu = new ToolStripMenuItem("Archivo");
            fileMenu.DropDownItems.Add("Configuración", null, (s, e) => OpenSettings());
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("Salir", null, (s, e) => this.Close());

            // Menú Documentos
            var documentsMenu = new ToolStripMenuItem("Documentos");
            documentsMenu.DropDownItems.Add("Escanear Documento", null, (s, e) => tabControl.SelectedIndex = 0);
            documentsMenu.DropDownItems.Add("Buscar Documentos", null, (s, e) => tabControl.SelectedIndex = 1);

            // Menú Ayuda
            var helpMenu = new ToolStripMenuItem("Ayuda");
            helpMenu.DropDownItems.Add("Acerca de", null, (s, e) => ShowAbout());

            // Menú Herramientas (solo para usuarios que no sean "User")
            var menuItems = new List<ToolStripItem> { fileMenu, documentsMenu };

            if (_authService.CurrentUser?.Role != "User")
            {
                var toolsMenu = new ToolStripMenuItem("Herramientas");
                toolsMenu.DropDownItems.Add("Logs de Auditoría", null, (s, e) => ShowAuditLogs());

                if (_authService.HasPermission("ManageUsers"))
                {
                    toolsMenu.DropDownItems.Add("Gestión de Usuarios", null, (s, e) => ShowUserManagement());
                }

                menuItems.Add(toolsMenu);
            }

            menuItems.Add(helpMenu);
            menuStrip.Items.AddRange(menuItems.ToArray());
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();

            var lblUser = new ToolStripStatusLabel($"Usuario: {_authService.CurrentUser?.FullName}");
            var lblStorage = new ToolStripStatusLabel($"Almacenamiento: {_storageService.CurrentStorageType}");
            var lblTime = new ToolStripStatusLabel(DateTime.Now.ToString("dd/MM/yyyy HH:mm"));

            statusStrip.Items.AddRange(new ToolStripItem[] { lblUser, lblStorage, lblTime });
            this.Controls.Add(statusStrip);

            // Timer para actualizar la hora
            var timer = new Timer { Interval = 60000 };
            timer.Tick += (s, e) => lblTime.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
            timer.Start();
        }

        private void CreateMainLayout()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };

            // Pestaña de Escaneo
            var scanTab = new TabPage("Escanear Documento");
            CreateScanTab(scanTab);
            tabControl.TabPages.Add(scanTab);

            // Pestaña de Búsqueda
            var searchTab = new TabPage("Buscar Documentos");
            CreateSearchTab(searchTab);
            tabControl.TabPages.Add(searchTab);

            this.Controls.Add(tabControl);
        }

        private void CreateScanTab(TabPage tabPage)
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Panel izquierdo - Controles
            var leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 300,
                Padding = new Padding(5)
            };

            var grpScan = new GroupBox
            {
                Text = "Escanear Documento",
                Location = new Point(5, 5),
                Size = new Size(280, 150)
            };

            btnScanDocument = new Button
            {
                Text = "Escanear desde Scanner",
                Location = new Point(10, 25),
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnScanDocument.Click += BtnScanDocument_Click;

            btnSelectFile = new Button
            {
                Text = "Seleccionar Archivo",
                Location = new Point(10, 65),
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSelectFile.Click += BtnSelectFile_Click;

            grpScan.Controls.AddRange(new Control[] { btnScanDocument, btnSelectFile });
            leftPanel.Controls.Add(grpScan);

            // Información del documento
            var grpInfo = new GroupBox
            {
                Text = "Información del Documento",
                Location = new Point(5, 165),
                Size = new Size(280, 200)
            };

            var lblDNI = new Label
            {
                Text = "DNI:",
                Location = new Point(10, 30),
                Size = new Size(40, 20)
            };

            txtDNI = new TextBox
            {
                Location = new Point(60, 28),
                Size = new Size(200, 23),
                MaxLength = 8
            };

            var lblNotes = new Label
            {
                Text = "Notas:",
                Location = new Point(10, 70),
                Size = new Size(50, 20)
            };

            txtNotes = new TextBox
            {
                Location = new Point(10, 95),
                Size = new Size(250, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            var btnSave = new Button
            {
                Text = "Guardar Documento",
                Location = new Point(10, 165),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            btnSave.Click += BtnSave_Click;

            grpInfo.Controls.AddRange(new Control[] { lblDNI, txtDNI, lblNotes, txtNotes, btnSave });
            leftPanel.Controls.Add(grpInfo);

            // Panel derecho - Vista previa
            var rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var grpPreview = new GroupBox
            {
                Text = "Vista Previa",
                Dock = DockStyle.Fill
            };

            picPreview = new PictureBox
            {
                Dock = DockStyle.Fill,
                SizeMode = PictureBoxSizeMode.Zoom,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };

            grpPreview.Controls.Add(picPreview);
            rightPanel.Controls.Add(grpPreview);

            panel.Controls.AddRange(new Control[] { leftPanel, rightPanel });
            tabPage.Controls.Add(panel);
        }

        private void CreateSearchTab(TabPage tabPage)
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Panel superior - Filtros de búsqueda
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                Padding = new Padding(5)
            };

            var grpSearch = new GroupBox
            {
                Text = "Filtros de Búsqueda",
                Dock = DockStyle.Fill
            };

            var lblSearchDNI = new Label
            {
                Text = "DNI:",
                Location = new Point(10, 25),
                Size = new Size(40, 20)
            };

            txtSearchDNI = new TextBox
            {
                Location = new Point(55, 23),
                Size = new Size(100, 23),
                MaxLength = 8
            };

            var lblFromDate = new Label
            {
                Text = "Desde:",
                Location = new Point(170, 25),
                Size = new Size(50, 20)
            };

            dtpFromDate = new DateTimePicker
            {
                Location = new Point(225, 23),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(-1)
            };

            var lblToDate = new Label
            {
                Text = "Hasta:",
                Location = new Point(360, 25),
                Size = new Size(50, 20)
            };

            dtpToDate = new DateTimePicker
            {
                Location = new Point(415, 23),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            btnSearch = new Button
            {
                Text = "Buscar",
                Location = new Point(550, 22),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSearch.Click += BtnSearch_Click;

            var btnClear = new Button
            {
                Text = "Limpiar",
                Location = new Point(640, 22),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnClear.Click += BtnClear_Click;

            grpSearch.Controls.AddRange(new Control[] {
                lblSearchDNI, txtSearchDNI, lblFromDate, dtpFromDate,
                lblToDate, dtpToDate, btnSearch, btnClear
            });
            topPanel.Controls.Add(grpSearch);

            // Panel principal - Grid de resultados
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            dgvDocuments = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            // Configurar columnas
            dgvDocuments.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Id",
                    HeaderText = "ID",
                    DataPropertyName = "Id",
                    Width = 60,
                    Visible = false
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DNI",
                    HeaderText = "DNI",
                    DataPropertyName = "DNI",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "FileName",
                    HeaderText = "Nombre del Archivo",
                    DataPropertyName = "FileName",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "FileType",
                    HeaderText = "Tipo",
                    DataPropertyName = "FileType",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "FileSize",
                    HeaderText = "Tamaño",
                    DataPropertyName = "FileSize",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreatedAt",
                    HeaderText = "Fecha Creación",
                    DataPropertyName = "CreatedAt",
                    Width = 130
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Notes",
                    HeaderText = "Notas",
                    DataPropertyName = "Notes",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "StorageType",
                    HeaderText = "Almacenamiento",
                    DataPropertyName = "StorageType",
                    Width = 120
                }
            });

            // Menú contextual para el grid
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Abrir Documento", null, OpenDocument_Click);
            contextMenu.Items.Add("Editar Notas", null, EditNotes_Click);
            contextMenu.Items.Add(new ToolStripSeparator());

            if (_authService.HasPermission("DeleteDocument"))
            {
                contextMenu.Items.Add("Eliminar", null, DeleteDocument_Click);
            }

            dgvDocuments.ContextMenuStrip = contextMenu;
            dgvDocuments.CellDoubleClick += DgvDocuments_CellDoubleClick;

            mainPanel.Controls.Add(dgvDocuments);

            panel.Controls.AddRange(new Control[] { topPanel, mainPanel });
            tabPage.Controls.Add(panel);
        }

        private async void BtnScanDocument_Click(object sender, EventArgs e)
        {
            try
            {
                btnScanDocument.Enabled = false;
                btnScanDocument.Text = "Escaneando...";

                // Verificar si hay scanners disponibles
                if (!_scannerService.IsScannerAvailable())
                {
                    MessageBox.Show("No se encontraron scanners disponibles en el sistema.\n" +
                                  "Verifique que el scanner esté conectado y encendido.",
                        "Scanner no disponible", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Mostrar diálogo de selección de scanner y escanear
                var scannedFilePath = await _scannerService.ScanDocumentWithUIAsync();

                if (!string.IsNullOrEmpty(scannedFilePath))
                {
                    await ProcessSelectedFile(scannedFilePath);

                    MessageBox.Show("Documento escaneado exitosamente.",
                        "Escaneo completado", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                // Si scannedFilePath es null, el usuario canceló el escaneo
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error scanning document");
                MessageBox.Show("Error al escanear documento: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnScanDocument.Enabled = true;
                btnScanDocument.Text = "Escanear desde Scanner";
            }
        }

        private async void BtnSelectFile_Click(object sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "Seleccionar Documento",
                    Filter = "Archivos de Imagen|*.jpg;*.jpeg;*.png;*.bmp;*.tiff|Archivos PDF|*.pdf|Todos los archivos|*.*",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    await ProcessSelectedFile(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error selecting file");
                MessageBox.Show("Error al seleccionar archivo: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task ProcessSelectedFile(string filePath)
        {
            try
            {
                // Mostrar vista previa
                if (IsImageFile(filePath))
                {
                    picPreview.Image = Image.FromFile(filePath);

                    // Extraer DNI usando OCR
                    var dni = await _ocrService.ExtractDNIFromImageAsync(filePath);
                    if (!string.IsNullOrEmpty(dni))
                    {
                        txtDNI.Text = dni;
                        MessageBox.Show($"DNI detectado automáticamente: {dni}",
                            "OCR", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("No se pudo detectar un DNI válido en el documento. Por favor ingrese manualmente.",
                            "OCR", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                else
                {
                    // Para PDFs u otros archivos, mostrar icono genérico
                    picPreview.Image = SystemIcons.Application.ToBitmap();
                }

                // Habilitar botón guardar
                var btnSave = tabControl.TabPages[0].Controls.OfType<Panel>().First()
                    .Controls.OfType<Panel>().First()
                    .Controls.OfType<GroupBox>().Skip(1).First()
                    .Controls.OfType<Button>().First();
                btnSave.Enabled = true;
                btnSave.Tag = filePath; // Guardar la ruta del archivo
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing selected file: {FilePath}", filePath);
                MessageBox.Show("Error al procesar el archivo: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtDNI.Text) || txtDNI.Text.Length != 8)
                {
                    MessageBox.Show("Por favor ingrese un DNI válido de 8 dígitos.",
                        "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtDNI.Focus();
                    return;
                }

                var btnSave = sender as Button;
                var filePath = btnSave?.Tag as string;

                if (string.IsNullOrEmpty(filePath))
                {
                    MessageBox.Show("No hay archivo seleccionado para guardar.",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                btnSave.Enabled = false;
                btnSave.Text = "Guardando...";

                var document = await _documentService.CreateDocumentAsync(txtDNI.Text, filePath, txtNotes.Text);

                if (document != null)
                {
                    MessageBox.Show("Documento guardado exitosamente.",
                        "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpiar formulario
                    ClearScanForm();
                }
                else
                {
                    MessageBox.Show("Error al guardar el documento.",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error saving document");
                MessageBox.Show("Error al guardar documento: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                var btnSave = sender as Button;
                if (btnSave != null)
                {
                    btnSave.Enabled = true;
                    btnSave.Text = "Guardar Documento";
                }
            }
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                btnSearch.Enabled = false;
                btnSearch.Text = "Buscando...";

                var searchTerm = txtSearchDNI.Text.Trim();
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                List<Document> documents;

                if (!string.IsNullOrEmpty(searchTerm))
                {
                    documents = await _documentService.GetDocumentsByDNIAsync(searchTerm);
                }
                else
                {
                    documents = await _documentService.SearchDocumentsAsync("", fromDate, toDate);
                }

                dgvDocuments.DataSource = documents;

                var statusLabel = statusStrip.Items[2] as ToolStripStatusLabel;
                if (statusLabel != null)
                {
                    statusLabel.Text = $"Encontrados: {documents.Count} documentos";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error searching documents");
                MessageBox.Show("Error al buscar documentos: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSearch.Enabled = true;
                btnSearch.Text = "Buscar";
            }
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            txtSearchDNI.Clear();
            dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            dtpToDate.Value = DateTime.Now;
            dgvDocuments.DataSource = null;
        }

        private async void DgvDocuments_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                await OpenSelectedDocument();
            }
        }

        private async void OpenDocument_Click(object sender, EventArgs e)
        {
            await OpenSelectedDocument();
        }

        private async Task OpenSelectedDocument()
        {
            try
            {
                if (dgvDocuments.SelectedRows.Count == 0)
                    return;

                var document = dgvDocuments.SelectedRows[0].DataBoundItem as Document;
                if (document == null)
                    return;

                var filePath = await _documentService.GetDocumentPathAsync(document.Id);
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });

                    await _auditService.LogAsync("View", "Document", document.Id,
                        _authService.CurrentUser?.Username, Environment.MachineName,
                        $"Document opened: {document.FileName}");
                }
                else
                {
                    MessageBox.Show("El archivo no se encuentra disponible.",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error opening document");
                MessageBox.Show("Error al abrir documento: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void EditNotes_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDocuments.SelectedRows.Count == 0)
                    return;

                var document = dgvDocuments.SelectedRows[0].DataBoundItem as Document;
                if (document == null)
                    return;

                var newNotes = Microsoft.VisualBasic.Interaction.InputBox(
                    "Ingrese las nuevas notas:", "Editar Notas", document.Notes);

                if (!string.IsNullOrEmpty(newNotes) && newNotes != document.Notes)
                {
                    var success = await _documentService.UpdateDocumentAsync(document.Id, newNotes);
                    if (success)
                    {
                        document.Notes = newNotes;
                        dgvDocuments.Refresh();
                        MessageBox.Show("Notas actualizadas exitosamente.",
                            "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("Error al actualizar las notas.",
                            "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error editing document notes");
                MessageBox.Show("Error al editar notas: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteDocument_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDocuments.SelectedRows.Count == 0)
                    return;

                var document = dgvDocuments.SelectedRows[0].DataBoundItem as Document;
                if (document == null)
                    return;

                var result = MessageBox.Show(
                    $"¿Está seguro que desea eliminar el documento '{document.FileName}'?\n\nEsta acción no se puede deshacer.",
                    "Confirmar Eliminación",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    var success = await _documentService.DeleteDocumentAsync(document.Id);
                    if (success)
                    {
                        // Remover de la grilla
                        var documents = dgvDocuments.DataSource as List<Document>;
                        documents?.Remove(document);
                        dgvDocuments.Refresh();

                        MessageBox.Show("Documento eliminado exitosamente.",
                            "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("Error al eliminar el documento.",
                            "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting document");
                MessageBox.Show("Error al eliminar documento: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenSettings()
        {
            try
            {
                using var settingsForm = new SettingsForm(_storageService, _authService);
                settingsForm.ShowDialog();

                // Actualizar status bar
                var lblStorage = statusStrip.Items[1] as ToolStripStatusLabel;
                if (lblStorage != null)
                {
                    lblStorage.Text = $"Almacenamiento: {_storageService.CurrentStorageType}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error opening settings");
                MessageBox.Show("Error al abrir configuración: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAuditLogs()
        {
            try
            {
                var auditForm = new AuditLogForm(_auditService, _authService);
                auditForm.Show();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error showing audit logs");
                MessageBox.Show("Error al mostrar logs de auditoría: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowUserManagement()
        {
            try
            {
                var userService = new UserService(_context, _auditService, _authService);
                var userForm = new UserManagementForm(userService, _auditService, _authService);
                userForm.Show();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error showing user management");
                MessageBox.Show("Error al mostrar gestión de usuarios: " + ex.Message,
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAbout()
        {
            MessageBox.Show(
                "Sistema de Gestión Documental\n" +
                "Versión 1.0\n\n" +
                "Desarrollado para Municipalidad de San Miguel\n" +
                "© 2025 - Todos los derechos reservados",
                "Acerca de",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void ClearScanForm()
        {
            txtDNI.Clear();
            txtNotes.Clear();
            picPreview.Image = null;

            var btnSave = tabControl.TabPages[0].Controls.OfType<Panel>().First()
                .Controls.OfType<Panel>().First()
                .Controls.OfType<GroupBox>().Skip(1).First()
                .Controls.OfType<Button>().First();
            btnSave.Enabled = false;
            btnSave.Tag = null;
        }

        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".gif" }.Contains(extension);
        }

        private void LoadUserInfo()
        {
            if (_authService.CurrentUser != null)
            {
                this.Text += $" - {_authService.CurrentUser.FullName} ({_authService.CurrentUser.Role})";
            }
        }

        private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                await _authService.LogoutAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error during logout");
            }
        }
    }
}
