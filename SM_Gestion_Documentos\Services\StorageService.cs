﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using System.IO;

namespace SM_Gestion_Documentos.Services
{
    public class StorageService
    {
        private readonly string _localStoragePath;
        private readonly string _networkStoragePath;
        public string CurrentStorageType { get; private set; } = "Local";

        public StorageService()
        {
            _localStoragePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents");
            _networkStoragePath = @"\\server\shared\documents"; // Configurar según necesidad

            EnsureDirectoryExists(_localStoragePath);
        }

        public void SetStorageType(string storageType)
        {
            CurrentStorageType = storageType;
            Log.Information("Storage type changed to: {StorageType}", storageType);
        }

        public async Task<string?> StoreFileAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                var destinationPath = GetDestinationPath(dni, fileName);
                var destinationDir = Path.GetDirectoryName(destinationPath);

                if (!string.IsNullOrEmpty(destinationDir))
                {
                    EnsureDirectoryExists(destinationDir);
                }

                switch (CurrentStorageType.ToLower())
                {
                    case "local":
                        return await StoreFileLocallyAsync(sourceFilePath, destinationPath);

                    case "network":
                        return await StoreFileOnNetworkAsync(sourceFilePath, destinationPath);

                    case "googledrive":
                        return await StoreFileOnGoogleDriveAsync(sourceFilePath, dni, fileName);

                    case "onedrive":
                        return await StoreFileOnOneDriveAsync(sourceFilePath, dni, fileName);

                    default:
                        Log.Warning("Unknown storage type: {StorageType}, using local storage", CurrentStorageType);
                        return await StoreFileLocallyAsync(sourceFilePath, destinationPath);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error storing file: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileLocallyAsync(string sourceFilePath, string destinationPath)
        {
            try
            {
                await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));
                Log.Information("File stored locally: {DestinationPath}", destinationPath);
                return destinationPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error storing file locally: {SourcePath} -> {DestinationPath}",
                    sourceFilePath, destinationPath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnNetworkAsync(string sourceFilePath, string destinationPath)
        {
            try
            {
                var networkPath = destinationPath.Replace(_localStoragePath, _networkStoragePath);
                var networkDir = Path.GetDirectoryName(networkPath);

                if (!string.IsNullOrEmpty(networkDir))
                {
                    EnsureDirectoryExists(networkDir);
                }

                await Task.Run(() => File.Copy(sourceFilePath, networkPath, true));
                Log.Information("File stored on network: {NetworkPath}", networkPath);
                return networkPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error storing file on network: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnGoogleDriveAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                // Implementación básica para Google Drive
                // Requiere configuración de credenciales OAuth2
                Log.Information("Google Drive storage not fully implemented. Using local storage.");
                return await StoreFileLocallyAsync(sourceFilePath, GetDestinationPath(dni, fileName));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error storing file on Google Drive: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnOneDriveAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                // Implementación básica para OneDrive
                // Requiere configuración de Microsoft Graph API
                Log.Information("OneDrive storage not fully implemented. Using local storage.");
                return await StoreFileLocallyAsync(sourceFilePath, GetDestinationPath(dni, fileName));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error storing file on OneDrive: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath, string storageType)
        {
            try
            {
                switch (storageType.ToLower())
                {
                    case "local":
                    case "network":
                        if (File.Exists(filePath))
                        {
                            await Task.Run(() => File.Delete(filePath));
                            Log.Information("File deleted: {FilePath}", filePath);
                            return true;
                        }
                        break;

                    case "googledrive":
                        // Implementar eliminación en Google Drive
                        Log.Information("Google Drive file deletion not implemented");
                        break;

                    case "onedrive":
                        // Implementar eliminación en OneDrive
                        Log.Information("OneDrive file deletion not implemented");
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting file: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<string?> GetFilePathAsync(string storedPath, string storageType)
        {
            try
            {
                switch (storageType.ToLower())
                {
                    case "local":
                    case "network":
                        return File.Exists(storedPath) ? storedPath : null;

                    case "googledrive":
                        // Implementar descarga temporal desde Google Drive
                        return await DownloadFromCloudAsync(storedPath, "googledrive");

                    case "onedrive":
                        // Implementar descarga temporal desde OneDrive
                        return await DownloadFromCloudAsync(storedPath, "onedrive");

                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting file path: {StoredPath}", storedPath);
                return null;
            }
        }

        private async Task<string?> DownloadFromCloudAsync(string cloudPath, string provider)
        {
            try
            {
                // Crear directorio temporal
                var tempDir = Path.Combine(Path.GetTempPath(), "DocumentManager");
                EnsureDirectoryExists(tempDir);

                var tempFile = Path.Combine(tempDir, Path.GetFileName(cloudPath));

                // Aquí implementarías la descarga real desde el proveedor de nube
                Log.Information("Cloud download not fully implemented for {Provider}", provider);

                return tempFile;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error downloading from cloud: {CloudPath}", cloudPath);
                return null;
            }
        }

        private string GetDestinationPath(string dni, string fileName)
        {
            var year = DateTime.Now.Year.ToString();
            var month = DateTime.Now.Month.ToString("00");

            var basePath = CurrentStorageType.ToLower() == "network" ? _networkStoragePath : _localStoragePath;

            return Path.Combine(basePath, year, month, dni, fileName);
        }

        private void EnsureDirectoryExists(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating directory: {Path}", path);
            }
        }

        public List<string> GetAvailableStorageTypes()
        {
            var types = new List<string> { "Local" };

            // Verificar disponibilidad de almacenamiento en red
            try
            {
                if (Directory.Exists(Path.GetDirectoryName(_networkStoragePath)))
                {
                    types.Add("Network");
                }
            }
            catch
            {
                // Red no disponible
            }

            // Agregar opciones de nube (requieren configuración)
            types.Add("GoogleDrive");
            types.Add("OneDrive");

            return types;
        }
    }
}
