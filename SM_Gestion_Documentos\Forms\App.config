﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<appSettings>
		<!-- Configuración de Base de Datos -->
		<add key="DatabasePath" value="DocumentManager.db" />

		<!-- Configuración de Almacenamiento -->
		<add key="StorageType" value="Local" />
		<add key="LocalStoragePath" value="Documents" />
		<add key="NetworkStoragePath" value="\\server\shared\documents" />

		<!-- Configuración de OCR -->
		<add key="TessDataPath" value="tessdata" />
		<add key="OCRLanguage" value="spa" />

		<!-- Configuración de Active Directory -->
		<add key="UseActiveDirectory" value="false" />
		<add key="ActiveDirectoryDomain" value="your-domain.com" />
		<add key="ActiveDirectoryServer" value="ldap://your-domain.com" />

		<!-- Configuración de Auditoría -->
		<add key="AuditLogRetentionDays" value="365" />
		<add key="EnableDetailedLogging" value="true" />

		<!-- Configuración de Logs -->
		<add key="LogFilePath" value="Logs\app-.log" />
		<add key="LogLevel" value="Information" />

		<!-- Configuración de Nube -->
		<add key="GoogleDriveEnabled" value="false" />
		<add key="OneDriveEnabled" value="false" />
		<add key="SharePointEnabled" value="false" />
	</appSettings>

	<connectionStrings>
		<add name="DefaultConnection"
			 connectionString="Data Source=DocumentManager.db"
			 providerName="Microsoft.Data.Sqlite" />
	</connectionStrings>

	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
	</startup>
</configuration>