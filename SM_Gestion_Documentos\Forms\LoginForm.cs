﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using SM_Gestion_Documentos.Services;
using SM_Gestion_Documentos.Data;
using Serilog;

namespace SM_Gestion_Documentos.Forms
{
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authService;
        private readonly AuditService _auditService;

        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private CheckBox chkActiveDirectory;
        private Label lblStatus;
        private PictureBox picLogo;

        public LoginForm(AuthenticationService authService, AuditService auditService)
        {
            _authService = authService;
            _auditService = auditService;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Sistema de Gestión Documental - Login";
            this.Size = new Size(400, 450);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.SuspendLayout();

            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(128, 128),
                SizeMode = PictureBoxSizeMode.StretchImage
            };
            try
            {
                // The path is relative to the executable directory.
                // The csproj file is configured to copy the assets folder to the output directory.
                string imagePath = Path.Combine("Forms", "assets", "321111965_458441543158373_8906602713859774882_n.jpg");
                if (File.Exists(imagePath))
                {
                    picLogo.Image = Image.FromFile(imagePath);
                }
                else
                {
                    // Fallback color or placeholder image if not found
                    picLogo.BackColor = Color.Gray;
                    Console.WriteLine($"Image not found at: {Path.GetFullPath(imagePath)}");
                }
            }
            catch (Exception ex)
            {
                // Log the error and show a placeholder
                Console.WriteLine($"Error loading logo: {ex.Message}");
                picLogo.BackColor = Color.Gray;
            }
            this.Controls.Add(picLogo);

            // Título
            var lblTitle = new Label
            {
                Text = "Municipalidad de San Miguel",
                Font = new Font("Arial", 14, FontStyle.Bold),
                Size = new Size(300, 25), // Increased width to fit the new title
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(lblTitle);

            var lblSubtitle = new Label
            {
                Text = "Sistema de Gestión Documental",
                Font = new Font("Arial", 9),
                Size = new Size(200, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(lblSubtitle);

            // Usuario
            var lblUsername = new Label
            {
                Text = "Usuario:",
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblUsername);

            txtUsername = new TextBox
            {
                Size = new Size(180, 23)
            };
            this.Controls.Add(txtUsername);

            // Contraseña
            var lblPassword = new Label
            {
                Text = "Contraseña:",
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblPassword);

            txtPassword = new TextBox
            {
                Size = new Size(180, 23),
                UseSystemPasswordChar = true
            };
            this.Controls.Add(txtPassword);

            // Active Directory
            chkActiveDirectory = new CheckBox
            {
                Text = "Usar Active Directory",
                Size = new Size(150, 20)
            };
            this.Controls.Add(chkActiveDirectory);

            // Botón Login
            btnLogin = new Button
            {
                Text = "Iniciar Sesión",
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnLogin.Click += BtnLogin_Click;
            this.Controls.Add(btnLogin);

            // Estado
            lblStatus = new Label
            {
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter,
                // Location and size will be set in the layout logic
            };
            this.Controls.Add(lblStatus);

            // Centering and layout logic
            Action layoutControls = () =>
            {
                int contentWidth = 260; // The width of the user/pass inputs + labels
                int startX = (this.ClientSize.Width - contentWidth) / 2;

                // Calculate total height of the centered content block
                int totalHeight = picLogo.Height + 20 +
                                  lblTitle.Height + 5 +
                                  lblSubtitle.Height + 25 +
                                  txtUsername.Height + 10 +
                                  txtPassword.Height + 15 +
                                  chkActiveDirectory.Height + 15 +
                                  btnLogin.Height;

                int currentY = (this.ClientSize.Height - totalHeight) / 2;
                if (currentY < 10) currentY = 10; // Ensure a minimum top margin

                // Position controls
                picLogo.Location = new Point((this.ClientSize.Width - picLogo.Width) / 2, currentY);
                currentY += picLogo.Height + 20;

                lblTitle.Location = new Point((this.ClientSize.Width - lblTitle.Width) / 2, currentY);
                currentY += lblTitle.Height + 5;

                lblSubtitle.Location = new Point((this.ClientSize.Width - lblSubtitle.Width) / 2, currentY);
                currentY += lblSubtitle.Height + 25;

                lblUsername.Location = new Point(startX, currentY);
                txtUsername.Location = new Point(startX + lblUsername.Width + 5, currentY - 2);
                currentY += txtUsername.Height + 10;

                lblPassword.Location = new Point(startX, currentY);
                txtPassword.Location = new Point(startX + lblPassword.Width + 5, currentY - 2);
                currentY += txtPassword.Height + 15;

                chkActiveDirectory.Location = new Point((this.ClientSize.Width - chkActiveDirectory.Width) / 2, currentY);
                currentY += chkActiveDirectory.Height + 15;

                btnLogin.Location = new Point((this.ClientSize.Width - btnLogin.Width) / 2, currentY);

                // Position status label at the bottom
                lblStatus.Size = new Size(this.ClientSize.Width - 40, 20);
                lblStatus.Location = new Point(20, this.ClientSize.Height - lblStatus.Height - 10);
            };

            // Run layout logic on load and resize
            this.Load += (s, e) => layoutControls();
            this.Resize += (s, e) => layoutControls();

            // Eventos
            txtPassword.KeyPress += TxtPassword_KeyPress;
            this.AcceptButton = btnLogin;

            this.ResumeLayout(true);
        }

        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private async void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                await PerformLoginAsync();
            }
        }

        private async Task PerformLoginAsync()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                lblStatus.Text = "Por favor ingrese usuario y contraseña";
                return;
            }

            btnLogin.Enabled = false;
            lblStatus.Text = "Iniciando sesión...";
            lblStatus.ForeColor = Color.Blue;

            try
            {
                var success = await _authService.LoginAsync(txtUsername.Text, txtPassword.Text, chkActiveDirectory.Checked);

                if (success)
                {
                    lblStatus.Text = "Inicio de sesión exitoso";
                    lblStatus.ForeColor = Color.Green;

                    Log.Information("User {Username} logged in successfully", txtUsername.Text);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "Usuario o contraseña incorrectos";
                    lblStatus.ForeColor = Color.Red;
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Error al iniciar sesión";
                lblStatus.ForeColor = Color.Red;
                Log.Error(ex, "Login error for user {Username}", txtUsername.Text);
            }
            finally
            {
                btnLogin.Enabled = true;
            }
        }
    }
}
