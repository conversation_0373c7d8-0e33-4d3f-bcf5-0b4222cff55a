using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SM_Gestion_Documentos.Models;
using SM_Gestion_Documentos.Services;
using Serilog;

namespace SM_Gestion_Documentos.Forms
{
    public partial class UserManagementForm : Form
    {
        private readonly UserService _userService;
        private readonly AuditService _auditService;
        private readonly AuthenticationService _authService;

        private DataGridView dgvUsers;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private TextBox txtSearch;
        private Button btnSearch;
        private BindingSource bindingSource;

        public UserManagementForm(UserService userService, AuditService auditService, AuthenticationService authService)
        {
            _userService = userService;
            _auditService = auditService;
            _authService = authService;
            bindingSource = new BindingSource();
            InitializeComponent();
            LoadUsers();
        }

        private void InitializeComponent()
        {
            this.Text = "Gestión de Usuarios";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Usar TableLayoutPanel para un layout más controlado
            var tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            // Configurar las filas: primera fija para controles, segunda expandible para grid
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 70));
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Panel superior - Búsqueda y botones
            var topPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 60
            };

            txtSearch = new TextBox
            {
                Location = new Point(10, 20),
                Size = new Size(200, 23),
                PlaceholderText = "Buscar usuario..."
            };

            btnSearch = new Button
            {
                Text = "Buscar",
                Location = new Point(220, 19),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSearch.Click += BtnSearch_Click;

            btnAdd = new Button
            {
                Text = "Agregar",
                Location = new Point(300, 19),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnAdd.Click += BtnAdd_Click;

            btnEdit = new Button
            {
                Text = "Editar",
                Location = new Point(380, 19),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            btnEdit.Click += BtnEdit_Click;

            btnDelete = new Button
            {
                Text = "Eliminar",
                Location = new Point(460, 19),
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnDelete.Click += BtnDelete_Click;

            btnRefresh = new Button
            {
                Text = "Actualizar",
                Location = new Point(540, 19),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnRefresh.Click += BtnRefresh_Click;

            topPanel.Controls.AddRange(new Control[] { txtSearch, btnSearch, btnAdd, btnEdit, btnDelete, btnRefresh });

            // DataGridView
            dgvUsers = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToOrderColumns = false
            };

            dgvUsers.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", DataPropertyName = "Id", Width = 50, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewTextBoxColumn { Name = "Username", HeaderText = "Usuario", DataPropertyName = "Username", Width = 120, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewTextBoxColumn { Name = "FullName", HeaderText = "Nombre Completo", DataPropertyName = "FullName", Width = 200, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "Email", DataPropertyName = "Email", Width = 180, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewTextBoxColumn { Name = "Role", HeaderText = "Rol", DataPropertyName = "Role", Width = 80, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "Activo", DataPropertyName = "IsActive", Width = 60, SortMode = DataGridViewColumnSortMode.Automatic },
                new DataGridViewTextBoxColumn { Name = "LastLogin", HeaderText = "Último Acceso", DataPropertyName = "LastLogin", Width = 130, SortMode = DataGridViewColumnSortMode.Automatic }
            });

            // Configurar BindingSource para sorting
            dgvUsers.DataSource = bindingSource;

            dgvUsers.CellFormatting += DgvUsers_CellFormatting;
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;

            // Agregar controles al TableLayoutPanel
            tableLayout.Controls.Add(topPanel, 0, 0);
            tableLayout.Controls.Add(dgvUsers, 0, 1);

            this.Controls.Add(tableLayout);
        }

        private void DgvUsers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvUsers.Rows[e.RowIndex].DataBoundItem is User user)
            {
                if (!user.IsActive)
                {
                    dgvUsers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
                    dgvUsers.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.Gray;
                }
            }
        }

        private void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        private async void LoadUsers()
        {
            try
            {
                var users = await _userService.GetAllUsersAsync();
                bindingSource.DataSource = users;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error loading users");
                MessageBox.Show($"Error al cargar usuarios: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                var users = await _userService.SearchUsersAsync(searchTerm);
                bindingSource.DataSource = users;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error searching users");
                MessageBox.Show($"Error al buscar usuarios: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var form = new UserEditForm(_userService, _auditService, _authService);
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadUsers();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0 && dgvUsers.SelectedRows[0].DataBoundItem is User user)
            {
                var form = new UserEditForm(_userService, _auditService, _authService, user);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                }
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0 && dgvUsers.SelectedRows[0].DataBoundItem is User user)
            {
                if (user.Username == _authService.CurrentUser?.Username)
                {
                    MessageBox.Show("No puede eliminar su propio usuario.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"¿Está seguro de eliminar el usuario '{user.Username}'?", 
                    "Confirmar eliminación", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _userService.DeleteUserAsync(user.Id);
                        LoadUsers();
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error deleting user");
                        MessageBox.Show($"Error al eliminar usuario: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }
    }
}