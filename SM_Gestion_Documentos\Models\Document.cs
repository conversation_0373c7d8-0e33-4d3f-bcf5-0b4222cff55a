﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SM_Gestion_Documentos.Models
{
    public class Document{
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(8)]
        public string DNI { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(50)]
        public string FileType { get; set; } = string.Empty;

        public long FileSize { get; set; }

        [MaxLength(20)]
        public string StorageType { get; set; } = "Local"; // Local, Network, GoogleDrive, OneDrive

        [MaxLength(255)]
        public string StorageId { get; set; } = string.Empty; // ID en el servicio de nube

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public int CreatedBy { get; set; }

        public DateTime? ModifiedAt { get; set; }

        public int? ModifiedBy { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
    }
}
