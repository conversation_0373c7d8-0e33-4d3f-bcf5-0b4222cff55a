﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;

namespace SM_Gestion_Documentos.Config
{
    public static class AppSettings
    {
        public static string DatabasePath =>
            ConfigurationManager.AppSettings["DatabasePath"] ??
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DocumentManager.db");

        public static string TessDataPath =>
            ConfigurationManager.AppSettings["TessDataPath"] ??
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tessdata");

        public static string LocalStoragePath =>
            ConfigurationManager.AppSettings["LocalStoragePath"] ??
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents");

        public static string NetworkStoragePath =>
            ConfigurationManager.AppSettings["NetworkStoragePath"] ??
            @"\\server\shared\documents";

        public static bool UseActiveDirectory =>
            bool.Parse(ConfigurationManager.AppSettings["UseActiveDirectory"] ?? "false");

        public static string ActiveDirectoryDomain =>
            ConfigurationManager.AppSettings["ActiveDirectoryDomain"] ?? "your-domain.com";

        public static int AuditLogRetentionDays =>
            int.Parse(ConfigurationManager.AppSettings["AuditLogRetentionDays"] ?? "365");

        public static string LogFilePath =>
            ConfigurationManager.AppSettings["LogFilePath"] ??
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", "app-.log");
    }
}
