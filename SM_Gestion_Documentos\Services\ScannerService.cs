using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using Serilog;

namespace SM_Gestion_Documentos.Services
{
    public class ScannerService
    {
        public class ScannerDevice
        {
            public string Id { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
        }

        public List<ScannerDevice> GetAvailableScanners()
        {
            var scanners = new List<ScannerDevice>();

            try
            {
                // Simulación de scanners disponibles
                // En una implementación real, aquí consultarías los dispositivos WIA
                scanners.Add(new ScannerDevice
                {
                    Id = "default_scanner",
                    Name = "Scanner del Sistema",
                    Description = "Scanner configurado por defecto en Windows"
                });

                Log.Information("Se encontraron {Count} dispositivos de escáner", scanners.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al obtener escáneres disponibles");
            }

            return scanners;
        }

        public async Task<string?> ScanDocumentAsync(string? scannerId = null, string? outputPath = null)
        {
            try
            {
                // Usar la aplicación de Windows Fax and Scan para escanear
                Log.Information("Starting scan using Windows Fax and Scan...");

                // Generar nombre de archivo si no se proporciona
                if (string.IsNullOrEmpty(outputPath))
                {
                    var tempDir = Path.Combine(Path.GetTempPath(), "SM_Scanner");
                    Directory.CreateDirectory(tempDir);
                    outputPath = Path.Combine(tempDir, $"scan_{DateTime.Now:yyyyMMdd_HHmmss}.jpg");
                }

                // Intentar abrir Windows Fax and Scan
                var processInfo = new ProcessStartInfo
                {
                    FileName = "WFS.exe",
                    Arguments = "/scan",
                    UseShellExecute = true
                };

                try
                {
                    using var process = Process.Start(processInfo);
                    if (process != null)
                    {
                        // Mostrar mensaje al usuario
                        MessageBox.Show(
                            "Se ha abierto la aplicación de Windows Fax and Scan.\n\n" +
                            "1. Realice el escaneo del documento\n" +
                            "2. Guarde la imagen en el escritorio\n" +
                            "3. Presione OK cuando termine\n\n" +
                            "Luego use 'Seleccionar Archivo' para cargar la imagen escaneada.",
                            "Escaneo de Documento",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);

                        Log.Information("Windows Fax and Scan opened successfully");
                        return null; // El usuario debe usar "Seleccionar Archivo" después
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "Could not open Windows Fax and Scan, falling back to file selection");

                    // Si no se puede abrir Windows Fax and Scan, mostrar diálogo de selección de archivo
                    MessageBox.Show(
                        "No se pudo abrir la aplicación de escaneo de Windows.\n\n" +
                        "Por favor, use su software de scanner preferido para escanear el documento\n" +
                        "y luego use 'Seleccionar Archivo' para cargarlo.",
                        "Scanner no disponible",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    return null;
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error scanning document");
                throw;
            }
        }

        public async Task<string?> ScanDocumentWithUIAsync()
        {
            try
            {
                return await ScanDocumentAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al escanear documento con interfaz");
                throw;
            }
        }

        public bool IsScannerAvailable()
        {
            try
            {
                // Verificar si Windows Fax and Scan está disponible
                var wfsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "WFS.exe");
                return File.Exists(wfsPath);
            }
            catch
            {
                return false;
            }
        }
    }
}
