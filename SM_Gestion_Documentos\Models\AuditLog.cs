﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace SM_Gestion_Documentos.Models
{
    public class AuditLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Action { get; set; } = string.Empty; // Login, Logout, Create, Update, Delete, View

        [MaxLength(100)]
        public string EntityType { get; set; } = string.Empty; // Document, User

        public int? EntityId { get; set; }

        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;

        [MaxLength(45)]
        public string IPAddress { get; set; } = string.Empty;

        [MaxLength(500)]
        public string Details { get; set; } = string.Empty;

        public DateTime Timestamp { get; set; } = DateTime.Now;

        public bool Success { get; set; } = true;

        [MaxLength(500)]
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
