using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using SM_Gestion_Documentos.Models;
using SM_Gestion_Documentos.Services;
using Serilog;

namespace SM_Gestion_Documentos.Forms
{
    public partial class UserEditForm : Form
    {
        private readonly UserService _userService;
        private readonly AuditService _auditService;
        private readonly AuthenticationService _authService;
        private readonly User? _user;
        private readonly bool _isEdit;

        private TextBox txtUsername;
        private TextBox txtFullName;
        private TextBox txtEmail;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private ComboBox cmbRole;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        public UserEditForm(UserService userService, AuditService auditService, AuthenticationService authService, User? user = null)
        {
            _userService = userService;
            _auditService = auditService;
            _authService = authService;
            _user = user;
            _isEdit = user != null;
            
            InitializeComponent();
            LoadUserData();
        }

        private void InitializeComponent()
        {
            this.Text = _isEdit ? "Editar Usuario" : "Nuevo Usuario";
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            var lblUsername = new Label { Text = "Usuario:", Location = new Point(20, 20), Size = new Size(80, 20) };
            txtUsername = new TextBox { Location = new Point(110, 18), Size = new Size(250, 23) };

            var lblFullName = new Label { Text = "Nombre:", Location = new Point(20, 50), Size = new Size(80, 20) };
            txtFullName = new TextBox { Location = new Point(110, 48), Size = new Size(250, 23) };

            var lblEmail = new Label { Text = "Email:", Location = new Point(20, 80), Size = new Size(80, 20) };
            txtEmail = new TextBox { Location = new Point(110, 78), Size = new Size(250, 23) };

            var lblPassword = new Label { Text = "Contraseña:", Location = new Point(20, 110), Size = new Size(80, 20) };
            txtPassword = new TextBox { Location = new Point(110, 108), Size = new Size(250, 23), UseSystemPasswordChar = true };

            var lblConfirm = new Label { Text = "Confirmar:", Location = new Point(20, 140), Size = new Size(80, 20) };
            txtConfirmPassword = new TextBox { Location = new Point(110, 138), Size = new Size(250, 23), UseSystemPasswordChar = true };

            var lblRole = new Label { Text = "Rol:", Location = new Point(20, 170), Size = new Size(80, 20) };
            cmbRole = new ComboBox { Location = new Point(110, 168), Size = new Size(150, 23), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbRole.Items.AddRange(new[] { "Admin", "User" });

            chkIsActive = new CheckBox { Text = "Usuario activo", Location = new Point(110, 200), Size = new Size(120, 20), Checked = true };

            btnSave = new Button
            {
                Text = "Guardar",
                Location = new Point(200, 250),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "Cancelar",
                Location = new Point(290, 250),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            this.Controls.AddRange(new Control[] {
                lblUsername, txtUsername, lblFullName, txtFullName, lblEmail, txtEmail,
                lblPassword, txtPassword, lblConfirm, txtConfirmPassword,
                lblRole, cmbRole, chkIsActive, btnSave, btnCancel
            });
        }

        private void LoadUserData()
        {
            if (_isEdit && _user != null)
            {
                txtUsername.Text = _user.Username;
                txtUsername.ReadOnly = true;
                txtFullName.Text = _user.FullName;
                txtEmail.Text = _user.Email;
                cmbRole.SelectedItem = _user.Role;
                chkIsActive.Checked = _user.IsActive;
            }
            else
            {
                cmbRole.SelectedItem = "User";
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "Guardando...";

                if (_isEdit)
                {
                    await UpdateUser();
                }
                else
                {
                    await CreateUser();
                }

                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error saving user");
                MessageBox.Show($"Error al guardar usuario: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "Guardar";
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("El nombre de usuario es requerido.", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("El nombre completo es requerido.", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!_isEdit && string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("La contraseña es requerida.", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("Las contraseñas no coinciden.", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private async Task CreateUser()
        {
            var user = new User
            {
                Username = txtUsername.Text.Trim(),
                FullName = txtFullName.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                Role = cmbRole.SelectedItem.ToString(),
                IsActive = chkIsActive.Checked
            };

            await _userService.CreateUserAsync(user, txtPassword.Text);
        }

        private async Task UpdateUser()
        {
            if (_user == null) return;

            _user.FullName = txtFullName.Text.Trim();
            _user.Email = txtEmail.Text.Trim();
            _user.Role = cmbRole.SelectedItem.ToString();
            _user.IsActive = chkIsActive.Checked;

            var password = string.IsNullOrWhiteSpace(txtPassword.Text) ? null : txtPassword.Text;
            await _userService.UpdateUserAsync(_user, password);
        }
    }
}