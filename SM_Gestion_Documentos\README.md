# Sistema de Gestión Documental - Municipalidad

## Descripción
Aplicación de escritorio desarrollada en C# .NET 8 para la gestión y organización de documentos municipales con reconocimiento óptico de caracteres (OCR) para extraer números de DNI automáticamente.

## Características Principales

### Funcionalidades Core
- **Escaneo y OCR**: Extracción automática de DNI de documentos escaneados
- **Organización Automática**: Almacenamiento estructurado por año/mes/DNI
- **Múltiples Tipos de Almacenamiento**: Local, Red, Google Drive, OneDrive
- **Búsqueda Avanzada**: Por DNI, fechas y contenido
- **Auditoría Completa**: Registro detallado de todas las operaciones

### Seguridad y Autenticación
- **Autenticación Dual**: Base de datos local o Active Directory
- **Control de Permisos**: Roles de usuario (Admin, Manager, User)
- **Logs de Auditoría**: Registro de accesos y operaciones
- **Encriptación**: Contraseñas hasheadas con BCrypt

## Requisitos del Sistema

### Software Requerido
- Windows 10/11 (64-bit)
- .NET 8 Runtime
- SQL Server Express o SQLite (incluido)

### Hardware Recomendado
- RAM: 4GB mínimo, 8GB recomendado
- Disco: 500MB para aplicación + espacio para documentos
- Scanner compatible con TWAIN (opcional)

## Instalación

### 1. Prerrequisitos
```bash
# Instalar .NET 8 Runtime
winget install Microsoft.DotNet.Runtime.8

# Instalar Visual C++ Redistributable (para Tesseract)
winget install Microsoft.VCRedist.2015+.x64
```

### 2. Configuración de Tesseract
1. Descargar tessdata desde: https://github.com/tesseract-ocr/tessdata
2. Crear carpeta `tessdata` en el directorio de la aplicación
3. Copiar archivos de idioma (esp.traineddata para español)

### 3. Primera Ejecución
- Usuario por defecto: `admin`
- Contraseña por defecto: `admin123`
- **IMPORTANTE**: Cambiar credenciales en el primer uso

## Configuración

### Almacenamiento Local
```
Documents/
├── 2025/
│   ├── 01/
│   │   ├── 12345678/
│   │   │   ├── 12345678_20250129_143022.pdf
│   │   │   └── 12345678_20250129_143155.jpg
```

### Almacenamiento en Red
- Configurar ruta UNC: `\\servidor\carpeta\documentos`
- Verificar permisos de escritura
- Probar conectividad desde la aplicación

### Active Directory
```xml
<add key="UseActiveDirectory" value="true" />
<add key="ActiveDirectoryDomain" value="municipalidad.local" />
```

## Uso de la Aplicación

### Escanear Documentos
1. Seleccionar "Escanear Documento" o "Seleccionar Archivo"
2. El sistema extraerá automáticamente el DNI (si es posible)
3. Verificar/corregir el DNI extraído
4. Agregar notas opcionales
5. Guardar documento

### Buscar Documentos
1. Ir a la pestaña "Buscar Documentos"
2. Filtrar por DNI, rango de fechas
3. Hacer doble clic para abrir documento
4. Usar menú contextual para editar/eliminar

### Administración
- **Logs de Auditoría**: Herramientas → Logs de Auditoría
- **Configuración**: Archivo → Configuración
- **Gestión de Usuarios**: Herramientas → Gestión de Usuarios (Admin)

## Estructura del Proyecto

```
DocumentManager/
├── Forms/              # Formularios de la aplicación
├── Services/           # Lógica de negocio
├── Models/             # Modelos de datos
├── Data/               # Contexto de base de datos
├── Config/             # Configuración
└── Resources/          # Recursos (iconos, imágenes)
```

## Tecnologías Utilizadas

- **.NET 8**: Framework principal
- **Windows Forms**: Interfaz de usuario
- **Entity Framework Core**: ORM para base de datos
- **SQLite**: Base de datos embebida
- **Tesseract.NET**: OCR para extracción de texto
- **Serilog**: Sistema de logging
- **BCrypt.NET**: Encriptación de contraseñas

## Solución de Problemas

### OCR No Funciona
- Verificar que existe la carpeta `tessdata`
- Descargar archivos de idioma desde repositorio oficial
- Verificar permisos de lectura en la carpeta

### Error de Base de Datos
- Verificar permisos de escritura en directorio de aplicación
- Eliminar archivo `DocumentManager.db` para recrear
- Revisar logs en carpeta `Logs/`

### Problemas de Red
- Verificar conectividad a carpeta compartida
- Comprobar credenciales de usuario
- Usar herramienta "Probar Conexión" en configuración

## Mantenimiento

### Limpieza de Logs
Los logs de auditoría se limpian automáticamente después de 365 días (configurable).

### Backup de Base de Datos
```bash
# Copiar archivo de base de datos
copy DocumentManager.db DocumentManager_backup_YYYYMMDD.db
```

### Actualización de Tesseract
1. Descargar nuevos archivos traineddata
2. Reemplazar en carpeta `tessdata`
3. Reiniciar aplicación

## Soporte

Para soporte técnico:
- Revisar logs en `Logs/app-YYYY-MM-DD.log`
- Verificar configuración en `app.config`
- Contactar al administrador del sistema

## Licencia

© 2025 - Sistema desarrollado para Municipalidad
Todos los derechos reservados.