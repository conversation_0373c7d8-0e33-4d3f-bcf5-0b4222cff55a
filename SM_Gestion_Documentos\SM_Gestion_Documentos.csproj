<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
	<OutputType>WinExe</OutputType>
	<TargetFramework>net8.0-windows</TargetFramework>
	<Nullable>enable</Nullable>
	<UseWindowsForms>true</UseWindowsForms>
	<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
		<PackageReference Include="Tesseract" Version="5.2.0" />
		<PackageReference Include="Serilog" Version="3.1.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
		<PackageReference Include="System.DirectoryServices" Version="8.0.0" />
		<PackageReference Include="Google.Apis.Drive.v3" Version="1.68.0.3411" />
		<PackageReference Include="Microsoft.Graph" Version="5.42.0" />
		<PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="NAPS2.Sdk" Version="1.2.0" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Forms\assets\" />
	</ItemGroup>

  <ItemGroup>
    <Content Include="Forms\assets\321111965_458441543158373_8906602713859774882_n.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>