﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Data;
using SM_Gestion_Documentos.Models;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace SM_Gestion_Documentos.Services
{
    public class DocumentService
    {
        private readonly DatabaseContext _context;
        private readonly AuditService _auditService;
        private readonly StorageService _storageService;
        private readonly AuthenticationService _authService;

        public DocumentService(DatabaseContext context, AuditService auditService,
            StorageService storageService, AuthenticationService authService)
        {
            _context = context;
            _auditService = auditService;
            _storageService = storageService;
            _authService = authService;
        }

        public async Task<Document?> CreateDocumentAsync(string dni, string filePath, string notes = "")
        {
            try
            {
                if (_authService.CurrentUser == null)
                {
                    Log.Warning("Attempt to create document without authentication");
                    return null;
                }

                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                {
                    Log.Error("File not found: {FilePath}", filePath);
                    return null;
                }

                // Generar nombre único para el archivo
                var fileName = $"{dni}_{DateTime.Now:yyyyMMdd_HHmmss}{fileInfo.Extension}";

                // Almacenar el archivo
                var storedPath = await _storageService.StoreFileAsync(filePath, dni, fileName);
                if (string.IsNullOrEmpty(storedPath))
                {
                    Log.Error("Failed to store file: {FilePath}", filePath);
                    return null;
                }

                var document = new Document
                {
                    DNI = dni,
                    FileName = fileName,
                    FilePath = storedPath,
                    FileType = fileInfo.Extension.ToLower(),
                    FileSize = fileInfo.Length,
                    StorageType = _storageService.CurrentStorageType,
                    Notes = notes,
                    CreatedBy = _authService.CurrentUser.Id
                };

                _context.Documents.Add(document);
                await _context.SaveChangesAsync();

                await _auditService.LogAsync("Create", "Document", document.Id,
                    _authService.CurrentUser.Username, Environment.MachineName,
                    $"Document created for DNI: {dni}");

                Log.Information("Document created successfully: {DocumentId} for DNI: {DNI}",
                    document.Id, dni);

                return document;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating document for DNI: {DNI}", dni);
                return null;
            }
        }

        public async Task<List<Document>> GetDocumentsByDNIAsync(string dni)
        {
            try
            {
                var documents = await _context.Documents
                    .Where(d => d.DNI == dni)
                    .OrderByDescending(d => d.CreatedAt)
                    .ToListAsync();

                if (_authService.CurrentUser != null)
                {
                    await _auditService.LogAsync("View", "Document", null,
                        _authService.CurrentUser.Username, Environment.MachineName,
                        $"Viewed documents for DNI: {dni}");
                }

                return documents;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving documents for DNI: {DNI}", dni);
                return new List<Document>();
            }
        }

        public async Task<List<Document>> SearchDocumentsAsync(string searchTerm, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Documents.AsQueryable();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(d => d.DNI.Contains(searchTerm) ||
                                           d.FileName.Contains(searchTerm) ||
                                           d.Notes.Contains(searchTerm));
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(d => d.CreatedAt >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(d => d.CreatedAt <= toDate.Value);
                }

                var documents = await query
                    .OrderByDescending(d => d.CreatedAt)
                    .Take(100) // Limitar resultados
                    .ToListAsync();

                if (_authService.CurrentUser != null)
                {
                    await _auditService.LogAsync("Search", "Document", null,
                        _authService.CurrentUser.Username, Environment.MachineName,
                        $"Document search: {searchTerm}");
                }

                return documents;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error searching documents with term: {SearchTerm}", searchTerm);
                return new List<Document>();
            }
        }

        public async Task<bool> UpdateDocumentAsync(int documentId, string notes)
        {
            try
            {
                if (_authService.CurrentUser == null)
                    return false;

                var document = await _context.Documents.FindAsync(documentId);
                if (document == null)
                {
                    Log.Warning("Document not found: {DocumentId}", documentId);
                    return false;
                }

                var oldNotes = document.Notes;
                document.Notes = notes;
                document.ModifiedAt = DateTime.Now;
                document.ModifiedBy = _authService.CurrentUser.Id;

                await _context.SaveChangesAsync();

                await _auditService.LogAsync("Update", "Document", documentId,
                    _authService.CurrentUser.Username, Environment.MachineName,
                    $"Document updated. Old notes: '{oldNotes}', New notes: '{notes}'");

                Log.Information("Document updated: {DocumentId}", documentId);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating document: {DocumentId}", documentId);
                return false;
            }
        }

        public async Task<bool> DeleteDocumentAsync(int documentId)
        {
            try
            {
                if (_authService.CurrentUser == null || !_authService.HasPermission("DeleteDocument"))
                    return false;

                var document = await _context.Documents.FindAsync(documentId);
                if (document == null)
                {
                    Log.Warning("Document not found for deletion: {DocumentId}", documentId);
                    return false;
                }

                // Eliminar archivo físico
                await _storageService.DeleteFileAsync(document.FilePath, document.StorageType);

                // Eliminar registro de la base de datos
                _context.Documents.Remove(document);
                await _context.SaveChangesAsync();

                await _auditService.LogAsync("Delete", "Document", documentId,
                    _authService.CurrentUser.Username, Environment.MachineName,
                    $"Document deleted: {document.FileName} for DNI: {document.DNI}");

                Log.Information("Document deleted: {DocumentId}", documentId);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting document: {DocumentId}", documentId);
                return false;
            }
        }

        public async Task<string?> GetDocumentPathAsync(int documentId)
        {
            try
            {
                var document = await _context.Documents.FindAsync(documentId);
                if (document == null)
                    return null;

                return await _storageService.GetFilePathAsync(document.FilePath, document.StorageType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting document path: {DocumentId}", documentId);
                return null;
            }
        }
    }
}
