﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SM_Gestion_Documentos.Services;

namespace SM_Gestion_Documentos.Forms
{
    public partial class SettingsForm : Form
    {
        private readonly StorageService _storageService;
        private readonly AuthenticationService _authService;

        private ComboBox cmbStorageType;
        private TextBox txtLocalPath;
        private TextBox txtNetworkPath;
        private Button btnBrowseLocal;
        private Button btnTestConnection;
        private CheckBox chkUseActiveDirectory;
        private TextBox txtADDomain;
        private Button btnSave;
        private Button btnCancel;

        public SettingsForm(StorageService storageService, AuthenticationService authService)
        {
            _storageService = storageService;
            _authService = authService;
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "Configuración del Sistema";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var tabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };

            // Pestaña de Almacenamiento
            var storageTab = new TabPage("Almacenamiento");
            CreateStorageTab(storageTab);
            tabControl.TabPages.Add(storageTab);

            // Pestaña de Autenticación
            var authTab = new TabPage("Autenticación");
            CreateAuthTab(authTab);
            tabControl.TabPages.Add(authTab);

            this.Controls.Add(tabControl);

            // Botones
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 50
            };

            btnSave = new Button
            {
                Text = "Guardar",
                Location = new Point(320, 10),
                Size = new Size(75, 30),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "Cancelar",
                Location = new Point(405, 10),
                Size = new Size(75, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnCancel.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });
            this.Controls.Add(buttonPanel);
        }

        private void CreateStorageTab(TabPage tabPage)
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var grpStorage = new GroupBox
            {
                Text = "Configuración de Almacenamiento",
                Location = new Point(10, 10),
                Size = new Size(460, 280)
            };

            var lblStorageType = new Label
            {
                Text = "Tipo de Almacenamiento:",
                Location = new Point(10, 30),
                Size = new Size(150, 20)
            };

            cmbStorageType = new ComboBox
            {
                Location = new Point(170, 28),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStorageType.Items.AddRange(_storageService.GetAvailableStorageTypes().ToArray());
            cmbStorageType.SelectedIndexChanged += CmbStorageType_SelectedIndexChanged;

            var lblLocalPath = new Label
            {
                Text = "Ruta Local:",
                Location = new Point(10, 70),
                Size = new Size(80, 20)
            };

            txtLocalPath = new TextBox
            {
                Location = new Point(100, 68),
                Size = new Size(280, 23)
            };

            btnBrowseLocal = new Button
            {
                Text = "...",
                Location = new Point(390, 68),
                Size = new Size(30, 23)
            };
            btnBrowseLocal.Click += BtnBrowseLocal_Click;

            var lblNetworkPath = new Label
            {
                Text = "Ruta de Red:",
                Location = new Point(10, 110),
                Size = new Size(80, 20)
            };

            txtNetworkPath = new TextBox
            {
                Location = new Point(100, 108),
                Size = new Size(280, 23)
            };

            btnTestConnection = new Button
            {
                Text = "Probar Conexión",
                Location = new Point(100, 150),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnTestConnection.Click += BtnTestConnection_Click;

            grpStorage.Controls.AddRange(new Control[] {
                lblStorageType, cmbStorageType, lblLocalPath, txtLocalPath, btnBrowseLocal,
                lblNetworkPath, txtNetworkPath, btnTestConnection
            });

            panel.Controls.Add(grpStorage);
            tabPage.Controls.Add(panel);
        }

        private void CreateAuthTab(TabPage tabPage)
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var grpAuth = new GroupBox
            {
                Text = "Configuración de Autenticación",
                Location = new Point(10, 10),
                Size = new Size(460, 200)
            };

            chkUseActiveDirectory = new CheckBox
            {
                Text = "Usar Active Directory",
                Location = new Point(10, 30),
                Size = new Size(200, 20)
            };
            chkUseActiveDirectory.CheckedChanged += ChkUseActiveDirectory_CheckedChanged;

            var lblADDomain = new Label
            {
                Text = "Dominio AD:",
                Location = new Point(10, 70),
                Size = new Size(80, 20)
            };

            txtADDomain = new TextBox
            {
                Location = new Point(100, 68),
                Size = new Size(200, 23),
                Enabled = false
            };

            var lblInfo = new Label
            {
                Text = "Nota: Los cambios en autenticación requieren reiniciar la aplicación.",
                Location = new Point(10, 110),
                Size = new Size(400, 40),
                ForeColor = Color.FromArgb(255, 193, 7)
            };

            grpAuth.Controls.AddRange(new Control[] {
                chkUseActiveDirectory, lblADDomain, txtADDomain, lblInfo
            });

            panel.Controls.Add(grpAuth);
            tabPage.Controls.Add(panel);
        }

        private void LoadCurrentSettings()
        {
            cmbStorageType.SelectedItem = _storageService.CurrentStorageType;
            txtLocalPath.Text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents");
            txtNetworkPath.Text = @"\\server\shared\documents";

            // Cargar configuración de AD desde archivo de configuración
            chkUseActiveDirectory.Checked = false; // Cargar desde config
            txtADDomain.Text = "your-domain.com"; // Cargar desde config
        }

        private void CmbStorageType_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedType = cmbStorageType.SelectedItem?.ToString();

            txtLocalPath.Enabled = selectedType == "Local";
            btnBrowseLocal.Enabled = selectedType == "Local";
            txtNetworkPath.Enabled = selectedType == "Network";
            btnTestConnection.Enabled = selectedType == "Network";
        }

        private void BtnBrowseLocal_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog
            {
                Description = "Seleccionar carpeta para almacenamiento local",
                SelectedPath = txtLocalPath.Text
            };

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtLocalPath.Text = folderDialog.SelectedPath;
            }
        }

        private void BtnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                var networkPath = txtNetworkPath.Text;
                if (string.IsNullOrWhiteSpace(networkPath))
                {
                    MessageBox.Show("Por favor ingrese una ruta de red.",
                        "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (Directory.Exists(networkPath))
                {
                    MessageBox.Show("Conexión exitosa a la ruta de red.",
                        "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No se puede acceder a la ruta de red especificada.",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al probar conexión: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChkUseActiveDirectory_CheckedChanged(object sender, EventArgs e)
        {
            txtADDomain.Enabled = chkUseActiveDirectory.Checked;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedStorageType = cmbStorageType.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(selectedStorageType))
                {
                    _storageService.SetStorageType(selectedStorageType);
                }

                // Aquí guardarías la configuración en un archivo de configuración
                // Por ejemplo, usando ConfigurationManager
                SaveConfiguration();

                MessageBox.Show("Configuración guardada exitosamente.",
                    "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al guardar configuración: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveConfiguration()
        {
            try
            {
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app.config");

                // Crear configuración básica si no existe
                if (!File.Exists(configPath))
                {
                    var defaultConfig = @"<?xml version=""1.0"" encoding=""utf-8""?>
<configuration>
  <appSettings>
    <add key=""StorageType"" value=""Local"" />
    <add key=""LocalStoragePath"" value=""Documents"" />
    <add key=""NetworkStoragePath"" value=""\\server\shared\documents"" />
    <add key=""UseActiveDirectory"" value=""false"" />
    <add key=""ActiveDirectoryDomain"" value=""your-domain.com"" />
  </appSettings>
</configuration>";
                    File.WriteAllText(configPath, defaultConfig);
                }

                // Actualizar configuración (implementación simplificada)
                var lines = File.ReadAllLines(configPath).ToList();
                UpdateConfigValue(lines, "StorageType", cmbStorageType.SelectedItem?.ToString() ?? "Local");
                UpdateConfigValue(lines, "LocalStoragePath", txtLocalPath.Text);
                UpdateConfigValue(lines, "NetworkStoragePath", txtNetworkPath.Text);
                UpdateConfigValue(lines, "UseActiveDirectory", chkUseActiveDirectory.Checked.ToString());
                UpdateConfigValue(lines, "ActiveDirectoryDomain", txtADDomain.Text);

                File.WriteAllLines(configPath, lines);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error al guardar archivo de configuración: {ex.Message}");
            }
        }

        private void UpdateConfigValue(List<string> lines, string key, string value)
        {
            var lineIndex = lines.FindIndex(l => l.Contains($"key=\"{key}\""));
            if (lineIndex >= 0)
            {
                lines[lineIndex] = $"    <add key=\"{key}\" value=\"{value}\" />";
            }
        }
    }
}
