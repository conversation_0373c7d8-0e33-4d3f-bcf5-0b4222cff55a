using System;
using System.Drawing;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using SM_Gestion_Documentos.Services;
using Serilog;

namespace SM_Gestion_Documentos.Forms
{
    public partial class ChangePasswordForm : Form
    {
        private readonly UserService _userService;
        private readonly AuthenticationService _authService;
        private readonly bool _isForced;

        private TextBox txtCurrentPassword;
        private TextBox txtNewPassword;
        private TextBox txtConfirmPassword;
        private Label lblPasswordStrength;
        private ProgressBar pbPasswordStrength;
        private Button btnChange;
        private Button btnCancel;
        private Label lblRequirements;

        public ChangePasswordForm(UserService userService, AuthenticationService authService, bool isForced = false)
        {
            _userService = userService;
            _authService = authService;
            _isForced = isForced;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = _isForced ? "Cambio de Contraseña Obligatorio" : "Cambiar Contraseña";
            this.Size = new Size(450, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Si es forzado, no permitir cerrar
            if (_isForced)
            {
                this.ControlBox = false;
            }

            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Mensaje informativo
            var lblMessage = new Label
            {
                Text = _isForced 
                    ? "Por seguridad, debe cambiar la contraseña por defecto antes de continuar."
                    : "Ingrese su contraseña actual y la nueva contraseña.",
                Location = new Point(0, 0),
                Size = new Size(400, 40),
                ForeColor = _isForced ? Color.Red : Color.Black,
                Font = new Font("Segoe UI", 9, _isForced ? FontStyle.Bold : FontStyle.Regular)
            };

            // Contraseña actual
            var lblCurrentPassword = new Label
            {
                Text = "Contraseña actual:",
                Location = new Point(0, 50),
                Size = new Size(120, 20)
            };

            txtCurrentPassword = new TextBox
            {
                Location = new Point(130, 48),
                Size = new Size(250, 23),
                UseSystemPasswordChar = true
            };

            // Nueva contraseña
            var lblNewPassword = new Label
            {
                Text = "Nueva contraseña:",
                Location = new Point(0, 85),
                Size = new Size(120, 20)
            };

            txtNewPassword = new TextBox
            {
                Location = new Point(130, 83),
                Size = new Size(250, 23),
                UseSystemPasswordChar = true
            };
            txtNewPassword.TextChanged += TxtNewPassword_TextChanged;

            // Confirmar contraseña
            var lblConfirmPassword = new Label
            {
                Text = "Confirmar contraseña:",
                Location = new Point(0, 120),
                Size = new Size(120, 20)
            };

            txtConfirmPassword = new TextBox
            {
                Location = new Point(130, 118),
                Size = new Size(250, 23),
                UseSystemPasswordChar = true
            };

            // Indicador de fortaleza
            lblPasswordStrength = new Label
            {
                Text = "Fortaleza de contraseña:",
                Location = new Point(0, 155),
                Size = new Size(150, 20)
            };

            pbPasswordStrength = new ProgressBar
            {
                Location = new Point(160, 153),
                Size = new Size(220, 20),
                Minimum = 0,
                Maximum = 100
            };

            // Requisitos
            lblRequirements = new Label
            {
                Text = "Requisitos:\n• Mínimo 8 caracteres\n• Al menos 1 mayúscula\n• Al menos 1 número\n• Al menos 1 símbolo (!@#$%^&*)",
                Location = new Point(0, 185),
                Size = new Size(400, 80),
                ForeColor = Color.Gray,
                Font = new Font("Segoe UI", 8)
            };

            // Botones
            btnChange = new Button
            {
                Text = "Cambiar Contraseña",
                Location = new Point(200, 280),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            btnChange.Click += BtnChange_Click;

            if (!_isForced)
            {
                btnCancel = new Button
                {
                    Text = "Cancelar",
                    Location = new Point(330, 280),
                    Size = new Size(80, 30),
                    BackColor = Color.FromArgb(108, 117, 125),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };
                btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;
                panel.Controls.Add(btnCancel);
            }

            panel.Controls.AddRange(new Control[] {
                lblMessage, lblCurrentPassword, txtCurrentPassword,
                lblNewPassword, txtNewPassword, lblConfirmPassword, txtConfirmPassword,
                lblPasswordStrength, pbPasswordStrength, lblRequirements, btnChange
            });

            this.Controls.Add(panel);
        }

        private void TxtNewPassword_TextChanged(object sender, EventArgs e)
        {
            var password = txtNewPassword.Text;
            var strength = CalculatePasswordStrength(password);
            
            pbPasswordStrength.Value = strength;
            
            if (strength < 60)
            {
                pbPasswordStrength.ForeColor = Color.Red;
                lblPasswordStrength.Text = "Fortaleza: Débil";
                lblPasswordStrength.ForeColor = Color.Red;
            }
            else if (strength < 80)
            {
                pbPasswordStrength.ForeColor = Color.Orange;
                lblPasswordStrength.Text = "Fortaleza: Media";
                lblPasswordStrength.ForeColor = Color.Orange;
            }
            else
            {
                pbPasswordStrength.ForeColor = Color.Green;
                lblPasswordStrength.Text = "Fortaleza: Fuerte";
                lblPasswordStrength.ForeColor = Color.Green;
            }

            btnChange.Enabled = IsPasswordValid(password) && !string.IsNullOrEmpty(txtCurrentPassword.Text);
        }

        private int CalculatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password)) return 0;

            int score = 0;

            // Longitud
            if (password.Length >= 8) score += 25;
            if (password.Length >= 12) score += 10;

            // Mayúsculas
            if (password.Any(char.IsUpper)) score += 20;

            // Minúsculas
            if (password.Any(char.IsLower)) score += 15;

            // Números
            if (password.Any(char.IsDigit)) score += 20;

            // Símbolos
            if (password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c))) score += 20;

            return Math.Min(score, 100);
        }

        private bool IsPasswordValid(string password)
        {
            if (string.IsNullOrEmpty(password) || password.Length < 8)
                return false;

            // Al menos una mayúscula
            if (!password.Any(char.IsUpper))
                return false;

            // Al menos un número
            if (!password.Any(char.IsDigit))
                return false;

            // Al menos un símbolo
            if (!password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
                return false;

            return true;
        }

        private async void BtnChange_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtCurrentPassword.Text))
                {
                    MessageBox.Show("Ingrese su contraseña actual.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (txtNewPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("Las contraseñas no coinciden.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!IsPasswordValid(txtNewPassword.Text))
                {
                    MessageBox.Show("La nueva contraseña no cumple con los requisitos de seguridad.", 
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                btnChange.Enabled = false;
                btnChange.Text = "Cambiando...";

                // Verificar contraseña actual
                var currentUser = _authService.CurrentUser;
                if (currentUser == null)
                {
                    MessageBox.Show("Error: Usuario no autenticado.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (!BCrypt.Net.BCrypt.Verify(txtCurrentPassword.Text, currentUser.PasswordHash))
                {
                    MessageBox.Show("La contraseña actual es incorrecta.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Cambiar contraseña
                await _userService.UpdateUserAsync(currentUser, txtNewPassword.Text);

                MessageBox.Show("Contraseña cambiada exitosamente.", "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error changing password");
                MessageBox.Show($"Error al cambiar contraseña: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnChange.Enabled = true;
                btnChange.Text = "Cambiar Contraseña";
            }
        }
    }
}
